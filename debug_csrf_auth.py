#!/usr/bin/env python3
"""
Debug script to test CSRF token and authentication issues
"""

import requests
import json
import sys

def test_csrf_token():
    """Test CSRF token generation"""
    print("=== Testing CSRF Token Generation ===")
    
    # Test without authentication
    print("\n1. Testing CSRF token without authentication:")
    try:
        response = requests.get('http://localhost:8080/api/csrf-token')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with authentication (you'll need to login first)
    print("\n2. Testing CSRF token with authentication:")
    print("Note: You need to login first to test this properly")
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Try to get CSRF token
    try:
        response = session.get('http://localhost:8080/api/csrf-token')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if 'csrf_token' in data:
                print(f"CSRF Token: {data['csrf_token'][:20]}...")
                return data['csrf_token']
    except Exception as e:
        print(f"Error: {e}")
    
    return None

def test_files_api(csrf_token=None):
    """Test /api/files endpoint"""
    print("\n=== Testing /api/files API ===")
    
    session = requests.Session()
    
    # Test without CSRF token
    print("\n1. Testing /api/files without CSRF token:")
    try:
        response = session.get('http://localhost:8080/api/files?category=MANUAL')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with CSRF token
    if csrf_token:
        print(f"\n2. Testing /api/files with CSRF token:")
        try:
            headers = {'X-CSRFToken': csrf_token}
            response = session.get('http://localhost:8080/api/files?category=MANUAL', headers=headers)
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text}")
        except Exception as e:
            print(f"Error: {e}")

def test_check_duplicate_api(csrf_token=None):
    """Test /api/check_duplicate endpoint"""
    print("\n=== Testing /api/check_duplicate API ===")
    
    session = requests.Session()
    
    # Test with JSON data
    print("\n1. Testing /api/check_duplicate with JSON data:")
    try:
        data = {
            'filename': 'test.pdf',
            'category': 'MANUAL'
        }
        headers = {'Content-Type': 'application/json'}
        if csrf_token:
            headers['X-CSRFToken'] = csrf_token
            
        response = session.post('http://localhost:8080/api/check_duplicate', 
                               json=data, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with form data
    print("\n2. Testing /api/check_duplicate with form data:")
    try:
        data = {
            'filename': 'test.pdf',
            'category': 'MANUAL'
        }
        headers = {}
        if csrf_token:
            headers['X-CSRFToken'] = csrf_token
            
        response = session.post('http://localhost:8080/api/check_duplicate', 
                               data=data, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

def test_authentication():
    """Test authentication flow"""
    print("\n=== Testing Authentication ===")
    
    session = requests.Session()
    
    # Test login page
    print("\n1. Testing login page accessibility:")
    try:
        response = session.get('http://localhost:8080/admin/login')
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("Login page accessible")
        else:
            print("Login page not accessible")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test auth status endpoint
    print("\n2. Testing auth status endpoint:")
    try:
        response = session.get('http://localhost:8080/api/auth/status')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Main function"""
    print("CSRF Token and Authentication Debug Script")
    print("=" * 50)
    
    # Test authentication
    test_authentication()
    
    # Test CSRF token
    csrf_token = test_csrf_token()
    
    # Test APIs
    test_files_api(csrf_token)
    test_check_duplicate_api(csrf_token)
    
    print("\n=== Debug Summary ===")
    print("1. Check if the Flask app is running on port 8080")
    print("2. Check if you're logged in to the admin interface")
    print("3. Check the Flask logs for detailed error messages")
    print("4. Verify CSRF token configuration in app/utils/config.py")

if __name__ == "__main__":
    main() 