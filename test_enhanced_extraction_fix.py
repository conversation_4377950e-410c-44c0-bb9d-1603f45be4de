#!/usr/bin/env python3
"""
Test script to verify that the enhanced title and author extraction functions
now correctly detect OCR PDFs and use the appropriate extraction method.
"""

import os
import sys
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.pdf_processor import (
    extract_enhanced_title,
    extract_enhanced_author,
    detect_ocr_pdf,
    extract_titles_and_authors_from_ocr_pdf,
    extract_titles_and_authors_from_pdf
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_extraction_fix():
    """Test the enhanced extraction functions with OCR detection."""
    
    # Test PDF path
    pdf_path = r"D:\erdb_ai_cursor\test_files\CANOPY\canopy_vol45n1.pdf"
    
    if not os.path.exists(pdf_path):
        logger.error(f"Test PDF not found: {pdf_path}")
        return
    
    logger.info("=" * 80)
    logger.info("TESTING ENHANCED EXTRACTION FIX")
    logger.info("=" * 80)
    
    # Step 1: Test OCR detection
    logger.info("\n1. Testing OCR Detection:")
    logger.info("-" * 40)
    
    ocr_detection = detect_ocr_pdf(pdf_path)
    logger.info(f"OCR Detection Result: {ocr_detection}")
    
    is_ocr = ocr_detection.get('is_ocr_pdf', False)
    confidence = ocr_detection.get('confidence', 0.0)
    
    logger.info(f"Is OCR PDF: {is_ocr}")
    logger.info(f"Confidence: {confidence:.2f}")
    
    # Step 2: Test direct OCR extraction (should use comprehensive method)
    logger.info("\n2. Testing Direct OCR Extraction:")
    logger.info("-" * 40)
    
    try:
        ocr_articles = extract_titles_and_authors_from_ocr_pdf(pdf_path, debug=True)
        logger.info(f"OCR Articles found: {len(ocr_articles)}")
        
        for i, article in enumerate(ocr_articles):
            logger.info(f"Article {i+1}:")
            logger.info(f"  Title: {article.get('title', 'N/A')}")
            logger.info(f"  Authors: <AUTHORS>
            logger.info(f"  Page: {article.get('page', 'N/A')}")
    except Exception as e:
        logger.error(f"OCR extraction failed: {str(e)}")
    
    # Step 3: Test direct standard extraction
    logger.info("\n3. Testing Direct Standard Extraction:")
    logger.info("-" * 40)
    
    try:
        standard_articles = extract_titles_and_authors_from_pdf(pdf_path)
        logger.info(f"Standard Articles found: {len(standard_articles)}")
        
        for i, article in enumerate(standard_articles):
            logger.info(f"Article {i+1}:")
            logger.info(f"  Title: {article.get('title', 'N/A')}")
            logger.info(f"  Authors: <AUTHORS>
            logger.info(f"  Page: {article.get('page', 'N/A')}")
    except Exception as e:
        logger.error(f"Standard extraction failed: {str(e)}")
    
    # Step 4: Test enhanced title extraction
    logger.info("\n4. Testing Enhanced Title Extraction:")
    logger.info("-" * 40)
    
    try:
        title_result = extract_enhanced_title(pdf_path)
        logger.info(f"Enhanced Title Result: {title_result}")
        
        if title_result.get('title'):
            logger.info(f"✅ Title extracted: {title_result['title']}")
            logger.info(f"✅ Method: {title_result['extraction_method']}")
        else:
            logger.warning("❌ No title extracted")
    except Exception as e:
        logger.error(f"Enhanced title extraction failed: {str(e)}")
    
    # Step 5: Test enhanced author extraction
    logger.info("\n5. Testing Enhanced Author Extraction:")
    logger.info("-" * 40)
    
    try:
        author_result = extract_enhanced_author(pdf_path)
        logger.info(f"Enhanced Author Result: {author_result}")
        
        if author_result.get('author'):
            logger.info(f"✅ Author extracted: {author_result['author']}")
            logger.info(f"✅ Method: {author_result['extraction_method']}")
        else:
            logger.warning("❌ No author extracted")
    except Exception as e:
        logger.error(f"Enhanced author extraction failed: {str(e)}")
    
    # Step 6: Summary and verification
    logger.info("\n6. Summary and Verification:")
    logger.info("-" * 40)
    
    logger.info(f"PDF: {os.path.basename(pdf_path)}")
    logger.info(f"OCR Detection: {is_ocr} (confidence: {confidence:.2f})")
    
    if is_ocr and confidence > 0.3:
        logger.info("✅ PDF correctly identified as OCR")
        expected_method = "ocr_analysis"
    else:
        logger.info("✅ PDF correctly identified as native")
        expected_method = "content_analysis"
    
    # Check if the enhanced functions used the correct method
    title_method = title_result.get('extraction_method')
    author_method = author_result.get('extraction_method')
    
    if title_method == expected_method:
        logger.info(f"✅ Title extraction used correct method: {title_method}")
    else:
        logger.warning(f"❌ Title extraction used wrong method: {title_method} (expected: {expected_method})")
    
    if author_method == expected_method:
        logger.info(f"✅ Author extraction used correct method: {author_method}")
    else:
        logger.warning(f"❌ Author extraction used wrong method: {author_method} (expected: {expected_method})")
    
    # Check for the specific title we're looking for
    title = title_result.get('title', '')
    if '45 YEARS' in title or 'COMMUNICATING SCIENCE' in title:
        logger.info("✅ Found expected title content!")
    else:
        logger.warning("❌ Expected title content not found")
    
    logger.info("\n" + "=" * 80)
    logger.info("TEST COMPLETED")
    logger.info("=" * 80)

if __name__ == "__main__":
    test_enhanced_extraction_fix() 