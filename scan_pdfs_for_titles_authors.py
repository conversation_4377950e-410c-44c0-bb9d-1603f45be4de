#!/usr/bin/env python3
"""
Comprehensive PDF Title and Author <PERSON><PERSON><PERSON>
Handles different PDF structures:
- CANOPY: Multiple articles with "What's Inside" section
- RISE: Single title on front page
"""

import sys
import os
from pathlib import Path
import logging

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.pdf_processor import (
    extract_titles_and_authors_with_tuning,
    extract_titles_and_authors_from_ocr_pdf,
    debug_spacy_status,
    detect_ocr_pdf,
    HAS_OCR,
    HAS_OPENCV
)

def setup_logging():
    """Setup logging for detailed output"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pdf_scan_debug.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def scan_pdf_structure(pdf_path, logger):
    """Analyze PDF structure and extract titles/authors"""
    logger.info(f"\n{'='*80}")
    logger.info(f"SCANNING PDF: {pdf_path}")
    logger.info(f"{'='*80}")
    
    # Check if it's an OCR PDF
    ocr_detection = detect_ocr_pdf(str(pdf_path))
    logger.info(f"OCR Detection: {ocr_detection}")
    
    # Determine PDF type based on filename
    pdf_name = Path(pdf_path).name.lower()
    is_canopy = 'canopy' in pdf_name
    is_rise = 'rise' in pdf_name
    
    logger.info(f"PDF Type: {'CANOPY' if is_canopy else 'RISE' if is_rise else 'UNKNOWN'}")
    
    # Test different extraction strategies
    strategies = [
        {
            "name": "Default Settings",
            "params": {
                "debug": True,
                "enable_logging": True,
                "skip_first_page": False  # Don't skip first page to catch "What's Inside"
            }
        },
        {
            "name": "Canopy-Specific (Multiple Articles)",
            "params": {
                "debug": True,
                "enable_logging": True,
                "skip_first_page": False,
                "min_title_font": 14,  # Lower threshold for canopy
                "min_title_length": 3,
                "max_title_gap": 50,
                "max_author_gap": 80
            }
        },
        {
            "name": "RISE-Specific (Single Title)",
            "params": {
                "debug": True,
                "enable_logging": True,
                "skip_first_page": False,
                "min_title_font": 18,  # Higher threshold for rise
                "min_title_length": 5,
                "max_title_gap": 30,
                "max_author_gap": 60
            }
        },
        {
            "name": "Relaxed Settings",
            "params": {
                "debug": True,
                "enable_logging": True,
                "skip_first_page": False,
                "min_title_font": 12,
                "min_title_length": 3,
                "max_title_gap": 100,
                "max_author_gap": 120,
                "min_author_font": 10,
                "max_author_font": 20
            }
        }
    ]
    
    best_results = []
    
    for strategy in strategies:
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing Strategy: {strategy['name']}")
        logger.info(f"{'='*60}")
        
        try:
            # Try native PDF extraction first
            articles = extract_titles_and_authors_with_tuning(
                str(pdf_path),
                **strategy['params']
            )
            
            if articles:
                logger.info(f"✅ Native extraction found {len(articles)} articles")
                for i, article in enumerate(articles):
                    logger.info(f"  {i+1}. Page {article['page']}, Col {article.get('column', 'N/A')}")
                    logger.info(f"     Title: {article['title']}")
                    logger.info(f"     Authors: <AUTHORS>
                
                best_results.append({
                    "strategy": strategy['name'],
                    "method": "native",
                    "articles": articles
                })
            else:
                logger.info("❌ Native extraction found no articles")
                
                # Try OCR extraction if available
                if HAS_OCR and HAS_OPENCV:
                    logger.info(f"\n🔄 Trying OCR extraction...")
                    ocr_articles = extract_titles_and_authors_from_ocr_pdf(
                        str(pdf_path),
                        debug=True,
                        enable_logging=True,
                        **{k: v for k, v in strategy['params'].items() if k not in ['debug', 'enable_logging']}
                    )
                    
                    if ocr_articles:
                        logger.info(f"✅ OCR extraction found {len(ocr_articles)} articles")
                        for i, article in enumerate(ocr_articles):
                            logger.info(f"  {i+1}. Page {article['page']}")
                            logger.info(f"     Title: {article['title']}")
                            logger.info(f"     Authors: <AUTHORS>
                        
                        best_results.append({
                            "strategy": strategy['name'],
                            "method": "ocr",
                            "articles": ocr_articles
                        })
                    else:
                        logger.info("❌ OCR extraction also found no articles")
                else:
                    logger.info("⚠️  OCR not available for fallback")
        
        except Exception as e:
            logger.error(f"❌ Error during extraction: {str(e)}")
            import traceback
            traceback.print_exc()
    
    return best_results

def analyze_results(results, pdf_path, logger):
    """Analyze and summarize the extraction results"""
    logger.info(f"\n{'='*80}")
    logger.info(f"ANALYSIS SUMMARY FOR: {pdf_path}")
    logger.info(f"{'='*80}")
    
    if not results:
        logger.info("❌ No successful extractions found")
        return
    
    # Find the strategy with the most articles
    best_strategy = max(results, key=lambda x: len(x['articles']))
    
    logger.info(f"📊 Best Strategy: {best_strategy['strategy']} ({best_strategy['method']})")
    logger.info(f"📄 Total Articles Found: {len(best_strategy['articles'])}")
    
    # Analyze the articles
    for i, article in enumerate(best_strategy['articles']):
        logger.info(f"\n📋 Article {i+1}:")
        logger.info(f"   Page: {article['page']}")
        logger.info(f"   Column: {article.get('column', 'N/A')}")
        logger.info(f"   Title: '{article['title']}'")
        logger.info(f"   Authors: <AUTHORS>
        
        # Check if this looks like a "What's Inside" section
        title_lower = article['title'].lower()
        if any(keyword in title_lower for keyword in ["what's inside", "contents", "table of contents", "articles"]):
            logger.info(f"   🔍 Likely 'What's Inside' section detected!")
        
        # Check if this looks like a main title
        if len(article['title']) > 20 and article['title'].isupper():
            logger.info(f"   🎯 Likely main title detected!")
    
    return best_strategy

def main():
    """Main function to scan all PDFs"""
    logger = setup_logging()
    
    logger.info("🔍 PDF Title and Author Scanner")
    logger.info("=" * 60)
    
    # Check spaCy status
    debug_spacy_status()
    
    # Check OCR dependencies
    logger.info(f"\n📋 OCR Dependencies:")
    logger.info(f"  OCR Available: {HAS_OCR}")
    logger.info(f"  OpenCV Available: {HAS_OPENCV}")
    
    # Find all PDFs
    test_dir = Path("test_files")
    canopy_pdfs = list(test_dir.glob("CANOPY/*.pdf"))
    rise_pdfs = list(test_dir.glob("RISE/*.pdf"))
    
    all_pdfs = canopy_pdfs + rise_pdfs
    
    if not all_pdfs:
        logger.error(f"❌ No PDF files found in {test_dir}/CANOPY/ or {test_dir}/RISE/")
        return
    
    logger.info(f"\n📁 Found {len(all_pdfs)} test PDFs:")
    for pdf in all_pdfs:
        logger.info(f"  - {pdf}")
    
    # Scan each PDF
    all_results = {}
    
    for pdf_path in all_pdfs:
        try:
            results = scan_pdf_structure(pdf_path, logger)
            best_result = analyze_results(results, pdf_path, logger)
            all_results[str(pdf_path)] = best_result
        except Exception as e:
            logger.error(f"❌ Error processing {pdf_path}: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # Final summary
    logger.info(f"\n{'='*80}")
    logger.info("FINAL SUMMARY")
    logger.info(f"{'='*80}")
    
    canopy_count = 0
    rise_count = 0
    
    for pdf_path, result in all_results.items():
        if result:
            pdf_name = Path(pdf_path).name.lower()
            if 'canopy' in pdf_name:
                canopy_count += len(result['articles'])
                logger.info(f"CANOPY: {Path(pdf_path).name} - {len(result['articles'])} articles")
            elif 'rise' in pdf_name:
                rise_count += len(result['articles'])
                logger.info(f"RISE: {Path(pdf_path).name} - {len(result['articles'])} articles")
    
    logger.info(f"\n📊 Total Results:")
    logger.info(f"  CANOPY PDFs: {canopy_count} total articles")
    logger.info(f"  RISE PDFs: {rise_count} total articles")
    logger.info(f"  Total: {canopy_count + rise_count} articles across all PDFs")

if __name__ == "__main__":
    main() 