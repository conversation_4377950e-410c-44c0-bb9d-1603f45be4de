#!/usr/bin/env python3
"""
Test script for comprehensive title and author extraction
Tests the new comprehensive extraction logic that properly handles OCR fragmentation
"""

import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app.services.pdf_processor import extract_titles_and_authors_comprehensive

def test_comprehensive_extraction():
    """Test the comprehensive title and author extraction logic"""

    # Path to the problematic PDF
    pdf_path = r"D:\erdb_ai_cursor\test_files\CANOPY\canopy_vol45n1.pdf"

    print("=" * 80)
    print("🧪 TESTING COMPREHENSIVE TITLE AND AUTHOR EXTRACTION")
    print("=" * 80)
    print(f"PDF Path: {pdf_path}")
    print(f"File exists: {os.path.exists(pdf_path)}")
    print()

    if not os.path.exists(pdf_path):
        print("❌ PDF file not found!")
        return

    # Test comprehensive extraction
    print("=" * 80)
    print("📄 TESTING COMPREHENSIVE EXTRACTION")
    print("=" * 80)

    try:
        articles = extract_titles_and_authors_comprehensive(
            pdf_path,
            debug=True
        )

        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(articles)} articles")

        for i, article in enumerate(articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            print(f"  Method: {article.get('extraction_method', 'N/A')}")

    except Exception as e:
        print(f"❌ Comprehensive extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()

    print("\n" + "=" * 80)
    print("✅ COMPREHENSIVE EXTRACTION TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_comprehensive_extraction() 