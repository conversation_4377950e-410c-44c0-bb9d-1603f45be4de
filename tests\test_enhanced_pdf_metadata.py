"""
Comprehensive test suite for enhanced PDF metadata extraction functionality.

Tests cover:
- Native PDF metadata extraction
- Metadata sanitization
- Enhanced title extraction with fallback strategies
- Enhanced author extraction with fallback strategies
- Database integration
- Edge cases and error handling
- Performance considerations
- Backward compatibility
"""

import unittest
import tempfile
import os
import sqlite3
from datetime import datetime
from unittest.mock import patch, MagicMock
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'app'))

from services.pdf_processor import (
    extract_pdf_native_metadata,
    sanitize_metadata,
    extract_enhanced_title,
    extract_enhanced_author,
    parse_pdf_date
)
from utils.content_db import create_gated_pdf_record, associate_pdf_with_url
from models.schema import initialize_database


class TestPDFMetadataExtraction(unittest.TestCase):
    """Test suite for PDF metadata extraction functions."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.test_db = os.path.join(self.test_dir, 'test.db')
        
        # Create a simple test PDF with metadata
        self.test_pdf_path = self.create_test_pdf_with_metadata()
        
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def create_test_pdf_with_metadata(self):
        """Create a test PDF file with metadata for testing."""
        try:
            import fitz  # PyMuPDF
            
            # Create a simple PDF with metadata
            doc = fitz.open()
            page = doc.new_page()
            page.insert_text((72, 72), "Test Document\n\nThis is a test PDF for metadata extraction.")
            
            # Set metadata
            metadata = {
                'title': 'Test Document Title',
                'author': 'Test Author',
                'subject': 'Test Subject',
                'keywords': 'test, metadata, extraction',
                'creator': 'Test Creator App',
                'producer': 'PyMuPDF Test',
                'creationDate': 'D:20230315143022+05\'30\'',
                'modDate': 'D:20230315143022+05\'30\''
            }
            doc.set_metadata(metadata)
            
            # Save the PDF
            pdf_path = os.path.join(self.test_dir, 'test_document.pdf')
            doc.save(pdf_path)
            doc.close()
            
            return pdf_path
        except ImportError:
            # If PyMuPDF is not available, create a dummy file
            pdf_path = os.path.join(self.test_dir, 'test_document.pdf')
            with open(pdf_path, 'wb') as f:
                f.write(b'%PDF-1.4\n%dummy PDF for testing')
            return pdf_path
    
    def test_extract_pdf_native_metadata(self):
        """Test native PDF metadata extraction."""
        try:
            metadata = extract_pdf_native_metadata(self.test_pdf_path)
            
            # Check that metadata dictionary has expected structure
            expected_keys = [
                'title', 'author', 'subject', 'keywords', 'creator', 'producer',
                'creation_date', 'modification_date', 'pdf_version', 'encrypted'
            ]
            for key in expected_keys:
                self.assertIn(key, metadata)
            
            # If PyMuPDF is available, check actual values
            if metadata.get('title'):
                self.assertEqual(metadata['title'], 'Test Document Title')
                self.assertEqual(metadata['author'], 'Test Author')
                self.assertEqual(metadata['subject'], 'Test Subject')
                self.assertEqual(metadata['keywords'], 'test, metadata, extraction')
                
        except Exception as e:
            # If PyMuPDF is not available, just check that function doesn't crash
            self.assertIsInstance(e, Exception)
    
    def test_parse_pdf_date(self):
        """Test PDF date parsing function."""
        # Test valid PDF date format
        date_str = 'D:20230315143022+05\'30\''
        parsed_date = parse_pdf_date(date_str)
        if parsed_date:
            self.assertEqual(parsed_date.year, 2023)
            self.assertEqual(parsed_date.month, 3)
            self.assertEqual(parsed_date.day, 15)
        
        # Test date without timezone
        date_str = 'D:20230315143022'
        parsed_date = parse_pdf_date(date_str)
        if parsed_date:
            self.assertEqual(parsed_date.year, 2023)
        
        # Test date only
        date_str = 'D:20230315'
        parsed_date = parse_pdf_date(date_str)
        if parsed_date:
            self.assertEqual(parsed_date.year, 2023)
        
        # Test invalid date
        parsed_date = parse_pdf_date('invalid_date')
        self.assertIsNone(parsed_date)
        
        # Test empty date
        parsed_date = parse_pdf_date('')
        self.assertIsNone(parsed_date)
    
    def test_sanitize_metadata(self):
        """Test metadata sanitization function."""
        # Test normal metadata
        metadata = {
            'title': 'Normal Title',
            'author': 'Normal Author',
            'subject': 'Normal Subject',
            'keywords': 'keyword1, keyword2',
            'creation_date': datetime(2023, 3, 15, 14, 30, 22),
            'encrypted': False
        }
        
        sanitized = sanitize_metadata(metadata)
        self.assertEqual(sanitized['title'], 'Normal Title')
        self.assertEqual(sanitized['author'], 'Normal Author')
        self.assertIsInstance(sanitized['creation_date'], datetime)
        self.assertFalse(sanitized['encrypted'])
        
        # Test metadata with control characters
        metadata_with_control = {
            'title': 'Title\x00with\x01control\x02chars',
            'author': 'Author\twith\ntabs\rand\rreturns',
            'subject': 'A' * 1500,  # Too long
            'keywords': None,
            'encrypted': 'true'  # String instead of boolean
        }
        
        sanitized = sanitize_metadata(metadata_with_control)
        self.assertNotIn('\x00', sanitized.get('title', ''))
        self.assertIn('\t', sanitized.get('author', ''))  # Tabs should be preserved
        self.assertLessEqual(len(sanitized.get('subject', '')), 1000)  # Should be truncated
        self.assertNotIn('keywords', sanitized)  # None values should be excluded
        self.assertTrue(sanitized['encrypted'])  # Should be converted to boolean
    
    def test_extract_enhanced_title_fallback(self):
        """Test enhanced title extraction with fallback strategies."""
        # Mock the existing extraction functions
        with patch('services.pdf_processor.extract_titles_and_authors_from_pdf') as mock_content, \
             patch('services.pdf_processor.extract_titles_and_authors_from_ocr_pdf') as mock_ocr:
            
            # Test fallback to content analysis
            mock_content.return_value = [{'title': 'Content Analysis Title', 'authors': 'Content Author'}]
            mock_ocr.return_value = []
            
            result = extract_enhanced_title(self.test_pdf_path, {'title': None})
            self.assertEqual(result['title'], 'Content Analysis Title')
            self.assertEqual(result['extraction_method'], 'content_analysis')
            
            # Test fallback to OCR analysis
            mock_content.return_value = []
            mock_ocr.return_value = [{'title': 'OCR Analysis Title', 'authors': 'OCR Author'}]
            
            result = extract_enhanced_title(self.test_pdf_path, {'title': None})
            self.assertEqual(result['title'], 'OCR Analysis Title')
            self.assertEqual(result['extraction_method'], 'ocr_analysis')
            
            # Test fallback to filename
            mock_content.return_value = []
            mock_ocr.return_value = []
            
            result = extract_enhanced_title(self.test_pdf_path, {'title': None})
            self.assertIsNotNone(result['title'])
            self.assertEqual(result['extraction_method'], 'filename')
    
    def test_extract_enhanced_author_fallback(self):
        """Test enhanced author extraction with fallback strategies."""
        with patch('services.pdf_processor.extract_titles_and_authors_from_pdf') as mock_content, \
             patch('services.pdf_processor.extract_titles_and_authors_from_ocr_pdf') as mock_ocr:
            
            # Test content analysis
            mock_content.return_value = [
                {'title': 'Title 1', 'authors': 'Author One'},
                {'title': 'Title 2', 'authors': 'Author Two'}
            ]
            
            result = extract_enhanced_author(self.test_pdf_path, {'author': None})
            self.assertEqual(result['author'], 'Author One; Author Two')
            self.assertEqual(result['extraction_method'], 'content_analysis')
            
            # Test OCR fallback
            mock_content.return_value = []
            mock_ocr.return_value = [{'title': 'OCR Title', 'authors': 'OCR Author'}]
            
            result = extract_enhanced_author(self.test_pdf_path, {'author': None})
            self.assertEqual(result['author'], 'OCR Author')
            self.assertEqual(result['extraction_method'], 'ocr_analysis')


class TestDatabaseIntegration(unittest.TestCase):
    """Test database integration for enhanced metadata."""

    def setUp(self):
        """Set up test database."""
        self.test_dir = tempfile.mkdtemp()
        self.test_db = os.path.join(self.test_dir, 'test.db')

        # Initialize test database
        with patch('app.models.schema.DB_PATH', self.test_db):
            initialize_database()

    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_create_gated_pdf_record_with_metadata(self):
        """Test creating gated PDF record with enhanced metadata."""
        pdf_metadata = {
            'pdf_title': 'Test Document Title',
            'pdf_author': 'Test Author',
            'pdf_subject': 'Test Subject',
            'pdf_keywords': 'test, keywords',
            'pdf_creation_date': datetime(2023, 3, 15, 14, 30, 22),
            'pdf_modification_date': datetime(2023, 3, 15, 15, 30, 22),
            'pdf_version': '1.7',
            'pdf_producer': 'Test Producer',
            'pdf_creator': 'Test Creator'
        }

        with patch('app.utils.content_db.get_db_connection') as mock_conn:
            mock_cursor = MagicMock()
            mock_conn.return_value.__enter__.return_value = MagicMock()
            mock_conn.return_value.__enter__.return_value.cursor.return_value = mock_cursor
            mock_cursor.lastrowid = 123

            pdf_id = create_gated_pdf_record(
                filename='test.pdf',
                original_filename='original.pdf',
                category='test',
                form_id=1,
                file_size=1024,
                page_count=5,
                pdf_metadata=pdf_metadata
            )

            self.assertEqual(pdf_id, 123)
            mock_cursor.execute.assert_called()

            # Check that the SQL includes the new metadata fields
            call_args = mock_cursor.execute.call_args[0]
            sql_query = call_args[0]
            sql_params = call_args[1]

            self.assertIn('pdf_title', sql_query)
            self.assertIn('pdf_author', sql_query)
            self.assertIn('Test Document Title', sql_params)
            self.assertIn('Test Author', sql_params)

    def test_associate_pdf_with_url_with_metadata(self):
        """Test associating PDF with URL including enhanced metadata."""
        pdf_metadata = {
            'pdf_title': 'URL Document Title',
            'pdf_author': 'URL Author',
            'pdf_subject': 'URL Subject'
        }

        with patch('app.utils.content_db.get_db_connection') as mock_conn:
            mock_cursor = MagicMock()
            mock_conn.return_value = MagicMock()
            mock_conn.return_value.cursor.return_value = mock_cursor
            mock_cursor.fetchone.return_value = None  # No existing record
            mock_cursor.lastrowid = 456

            pdf_id = associate_pdf_with_url(
                pdf_filename='test.pdf',
                category='test',
                original_filename='original.pdf',
                source_url_id=1,
                pdf_metadata=pdf_metadata
            )

            self.assertEqual(pdf_id, 456)

            # Verify INSERT was called with metadata
            insert_calls = [call for call in mock_cursor.execute.call_args_list if 'INSERT' in str(call)]
            self.assertTrue(len(insert_calls) > 0)


class TestEdgeCasesAndErrorHandling(unittest.TestCase):
    """Test edge cases and error handling."""

    def test_extract_metadata_from_nonexistent_file(self):
        """Test metadata extraction from non-existent file."""
        metadata = extract_pdf_native_metadata('/nonexistent/file.pdf')

        # Should return default metadata structure without crashing
        expected_keys = [
            'title', 'author', 'subject', 'keywords', 'creator', 'producer',
            'creation_date', 'modification_date', 'pdf_version', 'encrypted'
        ]
        for key in expected_keys:
            self.assertIn(key, metadata)

        # All values should be None or False for encrypted
        for key in expected_keys:
            if key == 'encrypted':
                self.assertFalse(metadata[key])
            else:
                self.assertIsNone(metadata[key])

    def test_sanitize_metadata_edge_cases(self):
        """Test metadata sanitization with edge cases."""
        # Test with None values
        metadata = {
            'title': None,
            'author': '',
            'subject': '   ',  # Only whitespace
            'keywords': 0,  # Non-string value
            'creation_date': 'not_a_date',  # Invalid date type
            'encrypted': 'maybe'  # Non-boolean value
        }

        sanitized = sanitize_metadata(metadata)

        # None and empty values should be excluded
        self.assertNotIn('title', sanitized)
        self.assertNotIn('author', sanitized)
        self.assertNotIn('subject', sanitized)

        # Non-string values should be converted
        self.assertEqual(sanitized.get('keywords'), '0')

        # Invalid date should be excluded
        self.assertNotIn('creation_date', sanitized)

        # Non-boolean should be converted to boolean
        self.assertTrue(sanitized['encrypted'])

    def test_enhanced_title_extraction_with_errors(self):
        """Test enhanced title extraction when extraction functions fail."""
        with patch('services.pdf_processor.extract_titles_and_authors_from_pdf', side_effect=Exception('Content extraction failed')), \
             patch('services.pdf_processor.extract_titles_and_authors_from_ocr_pdf', side_effect=Exception('OCR extraction failed')):

            # Should fall back to filename without crashing
            result = extract_enhanced_title('/test/path/sample_document.pdf')
            self.assertIsNotNone(result['title'])
            self.assertEqual(result['extraction_method'], 'filename')
            self.assertIn('Sample Document', result['title'])

    def test_enhanced_author_extraction_with_errors(self):
        """Test enhanced author extraction when extraction functions fail."""
        with patch('services.pdf_processor.extract_titles_and_authors_from_pdf', side_effect=Exception('Content extraction failed')), \
             patch('services.pdf_processor.extract_titles_and_authors_from_ocr_pdf', side_effect=Exception('OCR extraction failed')):

            # Should return None without crashing
            result = extract_enhanced_author('/test/path/sample_document.pdf')
            self.assertIsNone(result['author'])
            self.assertIsNone(result['extraction_method'])


class TestPerformanceAndBackwardCompatibility(unittest.TestCase):
    """Test performance considerations and backward compatibility."""

    def test_backward_compatibility_database_functions(self):
        """Test that database functions work without pdf_metadata parameter."""
        with patch('app.utils.content_db.get_db_connection') as mock_conn:
            mock_cursor = MagicMock()
            mock_conn.return_value.__enter__.return_value = MagicMock()
            mock_conn.return_value.__enter__.return_value.cursor.return_value = mock_cursor
            mock_cursor.lastrowid = 789

            # Test create_gated_pdf_record without pdf_metadata
            pdf_id = create_gated_pdf_record(
                filename='test.pdf',
                original_filename='original.pdf',
                category='test',
                form_id=1,
                file_size=1024,
                page_count=5
                # No pdf_metadata parameter
            )

            self.assertEqual(pdf_id, 789)
            mock_cursor.execute.assert_called()

            # Check that None values are handled properly
            call_args = mock_cursor.execute.call_args[0]
            sql_params = call_args[1]

            # Should have None values for metadata fields
            metadata_params = sql_params[-9:]  # Last 9 parameters are metadata fields
            for param in metadata_params:
                self.assertIsNone(param)

    def test_metadata_extraction_performance(self):
        """Test that metadata extraction doesn't significantly impact performance."""
        import time

        # Create a temporary test file
        test_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        test_file.write(b'%PDF-1.4\n%dummy PDF for performance testing')
        test_file.close()

        try:
            # Time the metadata extraction
            start_time = time.time()
            metadata = extract_pdf_native_metadata(test_file.name)
            end_time = time.time()

            # Should complete quickly (less than 1 second for a simple file)
            extraction_time = end_time - start_time
            self.assertLess(extraction_time, 1.0)

            # Should return proper structure even for dummy file
            self.assertIsInstance(metadata, dict)

        finally:
            os.unlink(test_file.name)


if __name__ == '__main__':
    unittest.main()
