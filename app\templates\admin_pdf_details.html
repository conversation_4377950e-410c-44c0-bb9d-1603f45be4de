{% extends "admin_base.html" %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid pt-4 px-4">
    <div class="row g-4">
        <div class="col-12">
            <div class="bg-light rounded h-100 p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold text-dark">
                        <i class="fas fa-file-pdf me-2 text-danger"></i>
                        PDF Document Details
                    </h1>
                    <div>
                        <a href="{{ url_for('list_files') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Files
                        </a>
                        {% if pdf_doc.filename %}
                        <a href="{{ url_for('download_gated_pdf', filename=pdf_doc.filename) }}" 
                           class="btn btn-primary ms-2">
                            <i class="fas fa-download me-2"></i>Download
                        </a>
                        {% endif %}
                    </div>
                </div>

                <!-- Basic Information Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold text-muted">Original Filename:</td>
                                        <td>{{ pdf_doc.original_filename or 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">System Filename:</td>
                                        <td><code>{{ pdf_doc.filename or 'N/A' }}</code></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Category:</td>
                                        <td><span class="badge bg-secondary">{{ pdf_doc.category or 'N/A' }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Upload Date:</td>
                                        <td>{{ pdf_doc.upload_date[:19] if pdf_doc.upload_date else 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Source URL:</td>
                                        <td>
                                            {% if pdf_doc.source_url %}
                                                <a href="{{ pdf_doc.source_url }}" target="_blank" class="text-primary">
                                                    {{ pdf_doc.source_url[:50] }}{% if pdf_doc.source_url|length > 50 %}...{% endif %}
                                                </a>
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold text-muted">File Size:</td>
                                        <td>
                                            {% if pdf_doc.file_size %}
                                                {% if pdf_doc.file_size > 1048576 %}
                                                    {{ (pdf_doc.file_size / 1024 / 1024) | round(2) }} MB
                                                {% else %}
                                                    {{ (pdf_doc.file_size / 1024) | round(1) }} KB
                                                {% endif %}
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Page Count:</td>
                                        <td>{{ pdf_doc.page_count or 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Database ID:</td>
                                        <td><code>{{ pdf_doc.id }}</code></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Form Submissions:</td>
                                        <td>
                                            <span class="badge bg-info">{{ submission_count }}</span>
                                            {% if associated_form %}
                                                <a href="{{ url_for('list_submissions') }}?pdf_id={{ pdf_doc.id }}" 
                                                   class="btn btn-sm btn-outline-info ms-2">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Associated Form:</td>
                                        <td>
                                            {% if associated_form %}
                                                <a href="{{ url_for('view_form', form_id=associated_form.id) }}" 
                                                   class="text-primary">
                                                    {{ associated_form.name }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">None</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced PDF Metadata Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>Enhanced PDF Metadata
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if pdf_doc.pdf_title or pdf_doc.pdf_author or pdf_doc.pdf_subject or pdf_doc.pdf_keywords %}
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold text-muted">Title:</td>
                                        <td>
                                            {% if pdf_doc.pdf_title %}
                                                <strong>{{ pdf_doc.pdf_title }}</strong>
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Author(s):</td>
                                        <td>
                                            {% if pdf_doc.pdf_author %}
                                                {{ pdf_doc.pdf_author }}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Subject:</td>
                                        <td>
                                            {% if pdf_doc.pdf_subject %}
                                                {{ pdf_doc.pdf_subject }}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Keywords:</td>
                                        <td>
                                            {% if pdf_doc.pdf_keywords %}
                                                {% for keyword in pdf_doc.pdf_keywords.split(',') %}
                                                    <span class="badge bg-light text-dark me-1">{{ keyword.strip() }}</span>
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold text-muted">Creation Date:</td>
                                        <td>
                                            {% if pdf_doc.pdf_creation_date %}
                                                {{ pdf_doc.pdf_creation_date[:19] if pdf_doc.pdf_creation_date else 'N/A' }}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Modification Date:</td>
                                        <td>
                                            {% if pdf_doc.pdf_modification_date %}
                                                {{ pdf_doc.pdf_modification_date[:19] if pdf_doc.pdf_modification_date else 'N/A' }}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">PDF Version:</td>
                                        <td>
                                            {% if pdf_doc.pdf_version %}
                                                <code>{{ pdf_doc.pdf_version }}</code>
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Creator:</td>
                                        <td>
                                            {% if pdf_doc.pdf_creator %}
                                                {{ pdf_doc.pdf_creator }}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-muted">Producer:</td>
                                        <td>
                                            {% if pdf_doc.pdf_producer %}
                                                {{ pdf_doc.pdf_producer }}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                            <p>No enhanced metadata available for this PDF.</p>
                            <p class="small">This may be because the PDF was uploaded before the enhanced metadata extraction feature was implemented, or the PDF doesn't contain extractable metadata.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Actions Card -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2 flex-wrap">
                            {% if pdf_doc.filename %}
                            <a href="{{ url_for('view_vector_data', category=pdf_doc.category, filename=pdf_doc.filename) }}" 
                               class="btn btn-info">
                                <i class="fas fa-search me-2"></i>View Vector Data
                            </a>
                            {% endif %}
                            
                            {% if associated_form %}
                            <a href="{{ url_for('view_form', form_id=associated_form.id) }}" 
                               class="btn btn-success">
                                <i class="fas fa-wpforms me-2"></i>View Associated Form
                            </a>
                            {% endif %}
                            
                            {% if submission_count > 0 %}
                            <a href="{{ url_for('list_submissions') }}?pdf_id={{ pdf_doc.id }}" 
                               class="btn btn-warning">
                                <i class="fas fa-clipboard-list me-2"></i>View Submissions ({{ submission_count }})
                            </a>
                            {% endif %}
                            
                            {% if pdf_doc.filename and pdf_doc.category %}
                            <form method="POST" action="{{ url_for('delete_file_route', category=pdf_doc.category, filename=pdf_doc.filename) }}"
                                  onsubmit="return confirm('Are you sure you want to delete this PDF and all its associated data? This action cannot be undone.');" 
                                  class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i>Delete PDF
                                </button>
                            </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
