# Enhanced PDF Text Extraction with PyMuPDF RAG and Context7 Integration

## Overview

This document describes the enhanced PDF text extraction system that integrates PyMuPDF RAG capabilities with advanced column detection, sentence orchestration, and Context7 integration for improved text extraction and RAG performance.

## Key Features

### 1. PyMuPDF RAG Integration
- **Advanced Text Extraction**: Uses `pymupdf4llm` for RAG-optimized text extraction
- **Table Detection**: Advanced table detection with multiple strategies
- **Word-Level Data**: Extracts word-level positioning and metadata
- **Layout Analysis**: Intelligent layout analysis for complex documents

### 2. Advanced Column Detection
- **Multi-Column Support**: Automatically detects and handles 2-5 column layouts
- **Column Clustering**: Uses spatial clustering to identify column boundaries
- **Reading Order**: Maintains proper reading order across columns
- **Column Metadata**: Provides detailed column information and positioning

### 3. Sentence Orchestration
- **Text Flow Improvement**: Enhances sentence flow and paragraph structure
- **OCR Issue Fixing**: Automatically fixes common OCR artifacts
- **Hyphenation Repair**: Reconnects broken words across line breaks
- **Whitespace Normalization**: Removes excessive whitespace and improves formatting

### 4. Context7 Integration
- **Semantic Enhancement**: Adds semantic annotations to extracted text
- **Content Classification**: Identifies and tags different content types
- **Metadata Enrichment**: Enhances text with contextual information
- **RAG Optimization**: Optimizes text for better retrieval performance

### 5. Context7-Enhanced Title and Author Extraction
- **Intelligent Title Validation**: Validates and enhances extracted titles with confidence scoring
- **Author Name Parsing**: Advanced author name parsing and validation
- **Academic Title Detection**: Identifies academic titles and institutional affiliations
- **Semantic Analysis**: Provides semantic analysis of titles and author information
- **Confidence Scoring**: Assigns confidence scores to extraction results
- **Validation and Suggestions**: Provides validation issues and improvement suggestions

## Configuration

### RAG Extraction Settings

The system uses a centralized configuration file (`config/rag_extraction_config.py`) with the following key settings:

```python
RAG_EXTRACTION_CONFIG = {
    "enabled": True,                    # Enable/disable RAG extraction
    "use_context7": False,              # Enable Context7 integration
    "column_detection": True,           # Enable column detection
    "sentence_orchestration": True,     # Enable sentence flow improvement
    "table_strategy": "advanced",       # Table detection strategy
    "extract_words": True,              # Extract word-level data
    "ignore_graphics": False,           # Ignore vector graphics
    "ignore_images": False,             # Ignore images during extraction
    "dpi": 300,                        # Image processing resolution
    "debug": False,                     # Enable debug output
    "min_text_threshold": 100,          # Minimum text for successful extraction
}
```

### Column Detection Settings

```python
COLUMN_DETECTION_CONFIG = {
    "threshold": 50,                    # Distance threshold for column separation
    "min_column_width": 100,            # Minimum width for a column
    "max_columns": 4,                   # Maximum number of columns to detect
    "sort_columns": True,               # Sort columns left to right
    "sort_text_within_columns": True,   # Sort text within each column
}
```

### Context7 API Configuration (for future use)
CONTEXT7_CONFIG = {
    "api_key": None,  # Set your Context7 API key here
    "base_url": "https://api.context7.com",
    "timeout": 30,
    "max_retries": 3,
}

# Context7 Title and Author Extraction Configuration
CONTEXT7_TITLE_AUTHOR_CONFIG = {
    "enabled": True,  # Enable Context7 enhancement for title/author extraction
    "confidence_threshold": 0.7,  # Minimum confidence threshold for accepting enhanced results
    "title_enhancement": True,  # Enable title enhancement
    "author_enhancement": True,  # Enable author enhancement
    "validation_enabled": True,  # Enable validation checks
    "semantic_analysis": True,  # Enable semantic analysis
    "spaCy_integration": True,  # Enable spaCy person name detection
    "academic_title_detection": True,  # Enable academic title detection
    "affiliation_detection": True,  # Enable institutional affiliation detection
    "context_validation": True,  # Enable PDF context validation
    "fallback_strategy": True,  # Enable fallback to standard methods
    "debug_mode": False,  # Enable debug output for Context7 functions
}

## Usage

### Basic RAG Extraction

```python
from app.services.pdf_processor import extract_text_with_rag

# Extract text with RAG capabilities
results = extract_text_with_rag(
    pdf_path="document.pdf",
    category="CANOPY",
    save_text=True,
    use_context7=False,
    column_detection=True,
    sentence_orchestration=True,
    table_strategy='advanced',
    extract_words=True,
    debug=False
)
```

### Context7-Enhanced Title and Author Extraction

```python
from app.services.pdf_processor import (
    extract_titles_and_authors_with_context7,
    extract_enhanced_title_with_context7,
    extract_enhanced_author_with_context7
)

# Extract titles and authors with Context7 enhancement
articles = extract_titles_and_authors_with_context7(
    pdf_path="document.pdf",
    use_context7=True,
    context7_confidence_threshold=0.7,
    debug=True
)

# Extract enhanced title with Context7
title_result = extract_enhanced_title_with_context7(
    pdf_path="document.pdf",
    use_context7=True
)

# Extract enhanced author with Context7
author_result = extract_enhanced_author_with_context7(
    pdf_path="document.pdf",
    use_context7=True
)
```

### Title and Author Enhancement

```python
from app.services.pdf_processor import (
    enhance_title_with_context7,
    enhance_authors_with_context7,
    validate_author_name
)

# Enhance a title with Context7
title_enhancement = enhance_title_with_context7(
    title="EFFECT OF CLIMATE CHANGE ON FOREST ECOSYSTEMS",
    metadata={'source': 'pdf_extraction'},
    pdf_context="Document about environmental research..."
)

# Enhance authors with Context7
authors_enhancement = enhance_authors_with_context7(
    authors="Dr. John Smith, PhD; Professor Jane Doe",
    metadata={'source': 'pdf_extraction'},
    pdf_context="Research paper on forestry..."
)

# Validate individual author names
validation = validate_author_name("Dr. Michael Chen, PhD")
```

### Advanced Column Extraction

```python
from app.services.pdf_processor import extract_text_with_advanced_columns

# Extract text with advanced column handling
results = extract_text_with_advanced_columns(
    pdf_path="document.pdf",
    category="CANOPY",
    save_text=True,
    detect_columns=True,
    sort_text=True,
    debug=True
)
```

### Full PDF Processing

```python
from app.services.pdf_processor import process_pdf

# Process PDF with enhanced extraction
result = process_pdf(
    pdf_path="document.pdf",
    category="CANOPY",
    extract_tables=False,
    save_images=False,
    save_tables=False
)
```

## Output Format

### RAG Extraction Results

Each page returns a dictionary with enhanced metadata:

```python
{
    'page': 1,
    'text': 'Extracted text content...',
    'extraction_method': 'rag_enhanced',
    'metadata': {
        'text_length': 1234,
        'word_count': 200,
        'has_tables': True,
        'has_images': False,
        'column_count': 2,
        'font_sizes': [12, 14, 16],
        'headers': ['Title', 'Subtitle'],
        'words': [...],  # Word-level data if enabled
        'text_file': '/path/to/saved/text.txt'
    }
}
```

### Column Detection Results

```python
{
    'page': 1,
    'text': 'Combined text from all columns...',
    'extraction_method': 'advanced_columns',
    'metadata': {
        'text_length': 1234,
        'word_count': 200,
        'column_count': 3,
        'column_details': [
            {
                'index': 0,
                'text': 'Column 1 text...',
                'span_count': 45,
                'bbox': (x0, y0, x1, y1)
            },
            # ... more columns
        ],
        'sort_applied': True
    }
}
```

## Performance Monitoring

The system includes comprehensive performance monitoring:

- **Memory Usage**: Tracks memory consumption during extraction
- **CPU Usage**: Monitors CPU utilization
- **Execution Time**: Measures processing time for each operation
- **Result Size**: Tracks output size and efficiency

## Fallback Strategy

The system implements a robust fallback strategy:

1. **RAG Extraction**: Primary method using PyMuPDF RAG
2. **Advanced Columns**: Fallback with column detection
3. **Standard Extraction**: Basic PyMuPDF text extraction
4. **OCR Extraction**: Final fallback for image-based PDFs

## Directory Structure

Enhanced extraction creates organized directory structures:

```
data/temp/
├── CANOPY/
│   └── document_name/
│       ├── pdf_images/
│       │   └── cover_image/
│       ├── pdf_tables/
│       └── pdf_text/          # New: Enhanced text storage
│           ├── document_name_page_1_rag.txt
│           ├── document_name_page_1_columns.txt
│           └── ...
```

## Integration with Vector Storage

The enhanced extraction integrates seamlessly with the existing vector storage system:

```python
from app.services.pdf_processor import pdf_to_documents

# Convert to LangChain documents with enhanced metadata
documents = pdf_to_documents(
    pdf_path="document.pdf",
    category="CANOPY"
)

# Documents include enhanced metadata for better retrieval
for doc in documents:
    print(f"Page: {doc.metadata['page']}")
    print(f"Method: {doc.metadata['extraction_method']}")
    print(f"Columns: {doc.metadata.get('column_count', 1)}")
    print(f"RAG Enhanced: {doc.metadata.get('rag_enhanced', False)}")
```

## Testing

Run the comprehensive test suite:

```bash
python test_rag_extraction.py
```

## API Reference

#### `enhance_text_with_context7()`
Enhance extracted text using Context7 capabilities.

**Parameters:**
- `text` (str): Extracted text to enhance
- `metadata` (dict): Page metadata for context

**Returns:** Enhanced text with semantic annotations

#### `extract_titles_and_authors_with_context7()`
Extract titles and authors with Context7 enhancement for improved accuracy and validation.

**Parameters:**
- `pdf_path` (str): Path to the PDF file
- `use_context7` (bool): Whether to use Context7 enhancement
- `context7_confidence_threshold` (float): Minimum confidence threshold for Context7 results
- `pdf_context` (str): Additional PDF context for validation
- `**kwargs`: Additional arguments passed to base extraction functions

**Returns:** List of dictionaries containing extracted articles with enhanced metadata

#### `extract_enhanced_title_with_context7()`
Extract document title using multiple strategies with Context7 enhancement.

**Parameters:**
- `pdf_path` (str): Path to the PDF file
- `native_metadata` (dict): Pre-extracted native metadata (optional)
- `use_context7` (bool): Whether to use Context7 enhancement

**Returns:** Dictionary with enhanced title information including confidence scores

#### `extract_enhanced_author_with_context7()`
Extract document author using multiple strategies with Context7 enhancement.

**Parameters:**
- `pdf_path` (str): Path to the PDF file
- `native_metadata` (dict): Pre-extracted native metadata (optional)
- `use_context7` (bool): Whether to use Context7 enhancement

**Returns:** Dictionary with enhanced author information including confidence scores

#### `enhance_title_with_context7()`
Enhance extracted title using Context7 for better validation and semantic understanding.

**Parameters:**
- `title` (str): Extracted title to enhance
- `metadata` (dict): Page metadata for context
- `pdf_context` (str): Additional PDF context for validation

**Returns:** Dictionary with enhanced title and validation results

#### `enhance_authors_with_context7()`
Enhance extracted authors using Context7 for better validation and parsing.

**Parameters:**
- `authors` (str): Extracted authors string to enhance
- `metadata` (dict): Page metadata for context
- `pdf_context` (str): Additional PDF context for validation

**Returns:** Dictionary with enhanced authors and validation results

#### `validate_author_name()`
Validate and analyze an individual author name.

**Parameters:**
- `author_name` (str): Author name to validate

**Returns:** Dictionary with validation results and suggestions

#### `is_rag_enabled()`
Check if RAG extraction is enabled.

**Returns:** Boolean indicating if RAG extraction is enabled

#### `get_context7_title_author_config()`
Get the Context7 title and author extraction configuration.

**Returns:** Dictionary with current Context7 title/author configuration settings

#### `update_context7_title_author_config(**kwargs)`
Update Context7 title and author extraction configuration with new values.

**Parameters:**
- `**kwargs`: Configuration key-value pairs to update

#### `is_context7_title_author_enabled()`
Check if Context7 title and author extraction is enabled.

**Returns:** Boolean indicating if Context7 title/author extraction is enabled

#### `get_context7_confidence_threshold()`
Get the Context7 confidence threshold for title/author extraction.

**Returns:** Float value representing the confidence threshold