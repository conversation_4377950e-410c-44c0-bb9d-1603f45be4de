"""
Enhanced Performance Logging System for ERDB

This module provides structured logging, alerting, and trend analysis
for performance monitoring with integration to external monitoring systems.
"""

import logging
import json
import os
import time
import smtplib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import threading
from collections import deque

# Import monitoring components with fallback
try:
    from app.utils.performance_monitor import get_performance_monitor, PerformanceMetric, BottleneckAlert
    HAS_PERFORMANCE_MONITOR = True
except ImportError:
    HAS_PERFORMANCE_MONITOR = False
    PerformanceMetric = None
    BottleneckAlert = None
    def get_performance_monitor():
        return None

try:
    from app.utils.health_monitor import get_health_monitor
    HAS_HEALTH_MONITOR = True
except ImportError:
    HAS_HEALTH_MONITOR = False
    def get_health_monitor():
        return None

# Configure structured logging
class PerformanceFormatter(logging.Formatter):
    """Custom formatter for performance logs."""
    
    def format(self, record):
        # Create structured log entry
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': getattr(record, 'module', None),
            'function': getattr(record, 'function', None),
            'execution_time': getattr(record, 'execution_time', None),
            'memory_usage': getattr(record, 'memory_usage', None),
            'error': getattr(record, 'error', None)
        }
        
        # Remove None values
        log_entry = {k: v for k, v in log_entry.items() if v is not None}
        
        return json.dumps(log_entry)

@dataclass
class AlertRule:
    """Configuration for performance alerts."""
    name: str
    condition: str  # 'execution_time', 'memory_usage', 'error_rate', 'system_health'
    threshold: float
    severity: str  # 'warning', 'critical'
    function_pattern: Optional[str] = None
    enabled: bool = True
    cooldown_minutes: int = 15

@dataclass
class AlertEvent:
    """Alert event information."""
    rule_name: str
    severity: str
    message: str
    timestamp: str
    function_name: Optional[str] = None
    metric_value: Optional[float] = None
    threshold: Optional[float] = None

class PerformanceLogger:
    """Enhanced performance logging and alerting system."""
    
    def __init__(self):
        self.setup_logging()
        self.alert_rules = self.load_alert_rules()
        self.alert_history = deque(maxlen=1000)
        self.last_alert_times = {}
        
        # Email configuration
        self.smtp_server = os.getenv('SMTP_SERVER')
        self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
        self.smtp_username = os.getenv('SMTP_USERNAME')
        self.smtp_password = os.getenv('SMTP_PASSWORD')
        self.alert_recipients = os.getenv('ALERT_RECIPIENTS', '').split(',')
        
        # Start background monitoring
        self.start_monitoring()
    
    def setup_logging(self):
        """Setup structured performance logging."""
        # Create performance logger
        self.logger = logging.getLogger('performance')
        self.logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Create file handler for performance logs
        log_dir = os.getenv('LOG_DIR', './logs')
        os.makedirs(log_dir, exist_ok=True)
        
        perf_log_file = os.path.join(log_dir, 'performance.log')
        file_handler = logging.FileHandler(perf_log_file)
        file_handler.setFormatter(PerformanceFormatter())
        self.logger.addHandler(file_handler)
        
        # Create console handler for development
        if os.getenv('FLASK_ENV') == 'development':
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(PerformanceFormatter())
            self.logger.addHandler(console_handler)
    
    def load_alert_rules(self) -> List[AlertRule]:
        """Load alert rules from configuration."""
        default_rules = [
            AlertRule(
                name="slow_function_execution",
                condition="execution_time",
                threshold=5.0,
                severity="warning",
                cooldown_minutes=10
            ),
            AlertRule(
                name="critical_function_execution",
                condition="execution_time", 
                threshold=15.0,
                severity="critical",
                cooldown_minutes=5
            ),
            AlertRule(
                name="high_memory_usage",
                condition="memory_usage",
                threshold=500.0,  # MB
                severity="warning",
                cooldown_minutes=15
            ),
            AlertRule(
                name="critical_memory_usage",
                condition="memory_usage",
                threshold=1000.0,  # MB
                severity="critical",
                cooldown_minutes=5
            ),
            AlertRule(
                name="high_error_rate",
                condition="error_rate",
                threshold=0.05,  # 5%
                severity="warning",
                cooldown_minutes=10
            ),
            AlertRule(
                name="system_health_warning",
                condition="system_health",
                threshold=70.0,  # Health score
                severity="warning",
                cooldown_minutes=30
            ),
            AlertRule(
                name="system_health_critical",
                condition="system_health",
                threshold=50.0,  # Health score
                severity="critical",
                cooldown_minutes=15
            )
        ]
        
        # Try to load custom rules from file
        rules_file = os.getenv('ALERT_RULES_FILE', './config/alert_rules.json')
        if os.path.exists(rules_file):
            try:
                with open(rules_file, 'r') as f:
                    rules_data = json.load(f)
                    return [AlertRule(**rule) for rule in rules_data]
            except Exception as e:
                self.logger.error(f"Failed to load alert rules from {rules_file}: {str(e)}")
        
        return default_rules
    
    def log_performance_metric(self, metric: PerformanceMetric):
        """Log a performance metric with structured format."""
        memory_usage = metric.memory_after_mb - metric.memory_before_mb
        
        # Create log record with extra fields
        extra = {
            'module': metric.module_name,
            'function': metric.function_name,
            'execution_time': metric.execution_time,
            'memory_usage': memory_usage,
            'error': metric.error
        }
        
        if metric.error:
            self.logger.error(
                f"Function {metric.module_name}.{metric.function_name} failed after {metric.execution_time:.3f}s",
                extra=extra
            )
        elif metric.execution_time > 5.0:
            self.logger.warning(
                f"Slow function execution: {metric.module_name}.{metric.function_name} took {metric.execution_time:.3f}s",
                extra=extra
            )
        else:
            self.logger.info(
                f"Function executed: {metric.module_name}.{metric.function_name} in {metric.execution_time:.3f}s",
                extra=extra
            )
        
        # Check alert rules
        self.check_alert_rules(metric)
    
    def check_alert_rules(self, metric: PerformanceMetric):
        """Check if any alert rules are triggered by the metric."""
        function_name = f"{metric.module_name}.{metric.function_name}"
        memory_usage = metric.memory_after_mb - metric.memory_before_mb
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # Check cooldown
            last_alert_key = f"{rule.name}:{function_name}"
            last_alert_time = self.last_alert_times.get(last_alert_key)
            if last_alert_time:
                cooldown_end = last_alert_time + timedelta(minutes=rule.cooldown_minutes)
                if datetime.now() < cooldown_end:
                    continue
            
            # Check function pattern if specified
            if rule.function_pattern and rule.function_pattern not in function_name:
                continue
            
            # Check conditions
            triggered = False
            metric_value = None
            
            if rule.condition == "execution_time":
                metric_value = metric.execution_time
                triggered = metric.execution_time > rule.threshold
            elif rule.condition == "memory_usage":
                metric_value = memory_usage
                triggered = memory_usage > rule.threshold
            elif rule.condition == "error_rate" and metric.error:
                # For error rate, we need to check recent error rate for this function
                triggered = self.check_error_rate(function_name, rule.threshold)
                metric_value = self.get_recent_error_rate(function_name)
            
            if triggered:
                alert = AlertEvent(
                    rule_name=rule.name,
                    severity=rule.severity,
                    message=f"Alert: {rule.name} triggered for {function_name}",
                    timestamp=datetime.now().isoformat(),
                    function_name=function_name,
                    metric_value=metric_value,
                    threshold=rule.threshold
                )
                
                self.trigger_alert(alert)
                self.last_alert_times[last_alert_key] = datetime.now()
    
    def check_system_health_alerts(self):
        """Check system health and trigger alerts if needed."""
        try:
            health_monitor = get_health_monitor()
            health_status = health_monitor.check_health_status()
            
            for rule in self.alert_rules:
                if rule.condition != "system_health" or not rule.enabled:
                    continue
                
                # Check cooldown
                last_alert_key = f"{rule.name}:system"
                last_alert_time = self.last_alert_times.get(last_alert_key)
                if last_alert_time:
                    cooldown_end = last_alert_time + timedelta(minutes=rule.cooldown_minutes)
                    if datetime.now() < cooldown_end:
                        continue
                
                if health_status.score < rule.threshold:
                    alert = AlertEvent(
                        rule_name=rule.name,
                        severity=rule.severity,
                        message=f"System health alert: Score {health_status.score} below threshold {rule.threshold}",
                        timestamp=datetime.now().isoformat(),
                        metric_value=health_status.score,
                        threshold=rule.threshold
                    )
                    
                    self.trigger_alert(alert)
                    self.last_alert_times[last_alert_key] = datetime.now()
        
        except Exception as e:
            self.logger.error(f"Error checking system health alerts: {str(e)}")
    
    def trigger_alert(self, alert: AlertEvent):
        """Trigger an alert through various channels."""
        self.alert_history.append(alert)
        
        # Log the alert
        self.logger.warning(f"ALERT: {alert.message}", extra={
            'alert_rule': alert.rule_name,
            'severity': alert.severity,
            'function': alert.function_name,
            'metric_value': alert.metric_value,
            'threshold': alert.threshold
        })
        
        # Send email alert if configured
        if self.smtp_server and self.alert_recipients:
            self.send_email_alert(alert)
        
        # Could add other alert channels here (Slack, PagerDuty, etc.)
    
    def send_email_alert(self, alert: AlertEvent):
        """Send email alert."""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.smtp_username
            msg['To'] = ', '.join(self.alert_recipients)
            msg['Subject'] = f"ERDB Performance Alert: {alert.severity.upper()}"
            
            body = f"""
            Performance Alert Triggered
            
            Rule: {alert.rule_name}
            Severity: {alert.severity.upper()}
            Message: {alert.message}
            Timestamp: {alert.timestamp}
            
            Function: {alert.function_name or 'N/A'}
            Metric Value: {alert.metric_value or 'N/A'}
            Threshold: {alert.threshold or 'N/A'}
            
            Please check the performance dashboard for more details.
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {str(e)}")
    
    def check_error_rate(self, function_name: str, threshold: float) -> bool:
        """Check if error rate for a function exceeds threshold."""
        return self.get_recent_error_rate(function_name) > threshold
    
    def get_recent_error_rate(self, function_name: str) -> float:
        """Get recent error rate for a function."""
        try:
            perf_monitor = get_performance_monitor()
            recent_metrics = perf_monitor.get_recent_metrics(hours=1)
            
            function_metrics = [m for m in recent_metrics 
                             if f"{m.module_name}.{m.function_name}" == function_name]
            
            if not function_metrics:
                return 0.0
            
            error_count = sum(1 for m in function_metrics if m.error)
            return error_count / len(function_metrics)
        
        except Exception:
            return 0.0
    
    def start_monitoring(self):
        """Start background monitoring thread."""
        def monitor_loop():
            while True:
                try:
                    # Check system health alerts every 5 minutes
                    self.check_system_health_alerts()
                    time.sleep(300)  # 5 minutes
                except Exception as e:
                    self.logger.error(f"Error in monitoring loop: {str(e)}")
                    time.sleep(60)  # Wait 1 minute before retrying
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def get_recent_alerts(self, hours: int = 24) -> List[AlertEvent]:
        """Get recent alerts within the specified time window."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        cutoff_str = cutoff_time.isoformat()
        
        return [alert for alert in self.alert_history 
                if alert.timestamp >= cutoff_str]
    
    def export_performance_logs(self, filepath: str, hours: int = 24) -> bool:
        """Export performance logs to file."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Read performance log file
            log_file = os.path.join(os.getenv('LOG_DIR', './logs'), 'performance.log')
            
            if not os.path.exists(log_file):
                return False
            
            exported_logs = []
            
            with open(log_file, 'r') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        log_time = datetime.fromisoformat(log_entry['timestamp'])
                        
                        if log_time >= cutoff_time:
                            exported_logs.append(log_entry)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
            
            # Export to file
            with open(filepath, 'w') as f:
                json.dump({
                    'export_timestamp': datetime.now().isoformat(),
                    'time_range_hours': hours,
                    'log_count': len(exported_logs),
                    'logs': exported_logs,
                    'recent_alerts': [asdict(alert) for alert in self.get_recent_alerts(hours)]
                }, f, indent=2)
            
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to export performance logs: {str(e)}")
            return False

# Global performance logger instance
_performance_logger: Optional[PerformanceLogger] = None

def get_performance_logger() -> PerformanceLogger:
    """Get the global performance logger instance."""
    global _performance_logger
    if _performance_logger is None:
        _performance_logger = PerformanceLogger()
    return _performance_logger

def log_performance_metric(metric: PerformanceMetric):
    """Log a performance metric."""
    logger = get_performance_logger()
    logger.log_performance_metric(metric)
