#!/usr/bin/env python3
"""
Test script for Context7-enhanced title and author extraction.
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_context7_title_author_extraction():
    """Test the Context7-enhanced title and author extraction."""
    
    # Test PDF path - use a sample PDF from the test files
    test_pdf_path = "test_files/CANOPY/canopy_v44n2.pdf"
    
    if not os.path.exists(test_pdf_path):
        logger.error(f"Test PDF not found: {test_pdf_path}")
        return False
    
    try:
        # Import the enhanced PDF processor functions
        from app.services.pdf_processor import (
            extract_titles_and_authors_with_context7,
            extract_enhanced_title_with_context7,
            extract_enhanced_author_with_context7,
            enhance_title_with_context7,
            enhance_authors_with_context7,
            validate_author_name
        )
        
        from config.rag_extraction_config import (
            get_context7_title_author_config,
            update_context7_title_author_config,
            is_context7_title_author_enabled
        )
        
        logger.info("Testing Context7-enhanced title and author extraction...")
        
        # Test 1: Configuration
        logger.info("Test 1: Configuration")
        config = get_context7_title_author_config()
        logger.info(f"Context7 title/author config: {config}")
        
        # Enable Context7 enhancement for testing
        update_context7_title_author_config(
            enabled=True,
            title_enhancement=True,
            author_enhancement=True,
            confidence_threshold=0.6,
            debug_mode=True
        )
        
        is_enabled = is_context7_title_author_enabled()
        logger.info(f"Context7 title/author extraction enabled: {is_enabled}")
        
        # Test 2: Title enhancement
        logger.info("Test 2: Title enhancement")
        test_titles = [
            "EFFECT OF CLIMATE CHANGE ON FOREST ECOSYSTEMS",
            "A Study of Biodiversity Patterns in Tropical Rainforests",
            "IMPLEMENTATION OF SUSTAINABLE FORESTRY PRACTICES",
            "short",
            "This is a very long title that exceeds the normal length limits and should trigger validation warnings for being too verbose and potentially problematic for processing",
            "Analysis of Environmental Impact Assessment Methodologies"
        ]
        
        for title in test_titles:
            enhancement = enhance_title_with_context7(title)
            logger.info(f"Title: '{title}'")
            logger.info(f"  Confidence: {enhancement['confidence']:.3f}")
            logger.info(f"  Enhanced: {enhancement['enhanced']}")
            logger.info(f"  Issues: {enhancement['validation_issues']}")
            logger.info(f"  Suggestions: {enhancement['suggestions']}")
            logger.info(f"  Patterns: {enhancement['semantic_analysis'].get('detected_patterns', [])}")
            logger.info("")
        
        # Test 3: Author enhancement
        logger.info("Test 3: Author enhancement")
        test_authors = [
            "John Smith, PhD",
            "Dr. Jane Doe and Robert Johnson",
            "Maria Garcia; Carlos Rodriguez; Dr. Sarah Wilson",
            "PROFESSOR JAMES BROWN",
            "invalid name with numbers 123",
            "Dr. Michael Chen, Professor Lisa Wang, and Associate Professor David Lee"
        ]
        
        for authors in test_authors:
            enhancement = enhance_authors_with_context7(authors)
            logger.info(f"Authors: <AUTHORS>
            logger.info(f"  Confidence: {enhancement['confidence']:.3f}")
            logger.info(f"  Enhanced: {enhancement['enhanced']}")
            logger.info(f"  Parsed authors: {len(enhancement['parsed_authors'])}")
            logger.info(f"  Issues: {enhancement['validation_issues']}")
            logger.info(f"  Suggestions: {enhancement['suggestions']}")
            logger.info("")
        
        # Test 4: Author name validation
        logger.info("Test 4: Author name validation")
        test_names = [
            "John Smith",
            "Dr. Jane Doe",
            "Maria Garcia Rodriguez",
            "PROFESSOR BROWN",
            "invalid123",
            "a",
            "Dr. Michael Chen, PhD"
        ]
        
        for name in test_names:
            validation = validate_author_name(name)
            logger.info(f"Name: '{name}'")
            logger.info(f"  Valid: {validation['valid']}")
            logger.info(f"  Confidence: {validation['confidence']:.3f}")
            logger.info(f"  Type: {validation['type']}")
            logger.info(f"  Suggestions: {validation['suggestions']}")
            logger.info("")
        
        # Test 5: Full Context7-enhanced extraction
        logger.info("Test 5: Full Context7-enhanced extraction")
        articles = extract_titles_and_authors_with_context7(
            test_pdf_path,
            use_context7=True,
            context7_confidence_threshold=0.6,
            debug=True
        )
        
        logger.info(f"Extracted {len(articles)} articles with Context7 enhancement")
        
        for i, article in enumerate(articles):
            logger.info(f"Article {i+1}:")
            logger.info(f"  Title: {article.get('title', 'N/A')}")
            logger.info(f"  Authors: <AUTHORS>
            logger.info(f"  Overall confidence: {article.get('overall_confidence', 0):.3f}")
            logger.info(f"  Title confidence: {article.get('title_confidence', 0):.3f}")
            logger.info(f"  Authors confidence: {article.get('authors_confidence', 0):.3f}")
            logger.info(f"  Extraction method: {article.get('extraction_method', 'N/A')}")
            
            if article.get('title_issues'):
                logger.info(f"  Title issues: {article['title_issues']}")
            if article.get('authors_issues'):
                logger.info(f"  Authors issues: {article['authors_issues']}")
            if article.get('parsed_authors'):
                logger.info(f"  Parsed authors: {len(article['parsed_authors'])}")
            logger.info("")
        
        # Test 6: Enhanced title extraction
        logger.info("Test 6: Enhanced title extraction")
        title_result = extract_enhanced_title_with_context7(
            test_pdf_path,
            use_context7=True
        )
        
        logger.info(f"Enhanced title result:")
        logger.info(f"  Title: {title_result.get('title', 'N/A')}")
        logger.info(f"  Method: {title_result.get('extraction_method', 'N/A')}")
        logger.info(f"  Confidence: {title_result.get('confidence', 0):.3f}")
        logger.info(f"  Enhanced: {title_result.get('enhanced', False)}")
        logger.info(f"  Issues: {title_result.get('validation_issues', [])}")
        logger.info(f"  Suggestions: {title_result.get('suggestions', [])}")
        logger.info("")
        
        # Test 7: Enhanced author extraction
        logger.info("Test 7: Enhanced author extraction")
        author_result = extract_enhanced_author_with_context7(
            test_pdf_path,
            use_context7=True
        )
        
        logger.info(f"Enhanced author result:")
        logger.info(f"  Author: {author_result.get('author', 'N/A')}")
        logger.info(f"  Method: {author_result.get('extraction_method', 'N/A')}")
        logger.info(f"  Confidence: {author_result.get('confidence', 0):.3f}")
        logger.info(f"  Enhanced: {author_result.get('enhanced', False)}")
        logger.info(f"  Parsed authors: {len(author_result.get('parsed_authors', []))}")
        logger.info(f"  Issues: {author_result.get('validation_issues', [])}")
        logger.info(f"  Suggestions: {author_result.get('suggestions', [])}")
        logger.info("")
        
        # Test 8: Performance and memory usage
        logger.info("Test 8: Performance testing")
        import time
        import psutil
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        start_time = time.time()
        
        # Run multiple extractions to test performance
        for i in range(3):
            articles = extract_titles_and_authors_with_context7(
                test_pdf_path,
                use_context7=True,
                context7_confidence_threshold=0.6
            )
        
        end_time = time.time()
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        logger.info(f"Performance results:")
        logger.info(f"  Total time: {end_time - start_time:.3f} seconds")
        logger.info(f"  Average time per extraction: {(end_time - start_time) / 3:.3f} seconds")
        logger.info(f"  Initial memory: {initial_memory:.1f} MB")
        logger.info(f"  Final memory: {final_memory:.1f} MB")
        logger.info(f"  Memory increase: {final_memory - initial_memory:.1f} MB")
        
        logger.info("All Context7 title and author extraction tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error during Context7 title and author extraction testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_functions():
    """Test the configuration functions."""
    try:
        from config.rag_extraction_config import (
            get_context7_title_author_config,
            update_context7_title_author_config,
            is_context7_title_author_enabled,
            get_context7_confidence_threshold
        )
        
        logger.info("Testing configuration functions...")
        
        # Test initial configuration
        config = get_context7_title_author_config()
        logger.info(f"Initial config: {config}")
        
        # Test configuration update
        update_context7_title_author_config(
            enabled=True,
            confidence_threshold=0.8,
            debug_mode=True
        )
        
        updated_config = get_context7_title_author_config()
        logger.info(f"Updated config: {updated_config}")
        
        # Test enabled check
        is_enabled = is_context7_title_author_enabled()
        logger.info(f"Context7 enabled: {is_enabled}")
        
        # Test confidence threshold
        threshold = get_context7_confidence_threshold()
        logger.info(f"Confidence threshold: {threshold}")
        
        logger.info("Configuration function tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error during configuration testing: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting Context7 title and author extraction tests...")
    
    # Test configuration functions
    config_success = test_configuration_functions()
    
    # Test main functionality
    main_success = test_context7_title_author_extraction()
    
    if config_success and main_success:
        logger.info("All tests passed successfully!")
        sys.exit(0)
    else:
        logger.error("Some tests failed!")
        sys.exit(1) 