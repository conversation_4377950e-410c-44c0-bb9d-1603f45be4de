#!/usr/bin/env python3
"""
Simple test script to verify improved title and author detection.
"""

import re

def validate_author_name(author_name):
    """
    Validate and analyze an individual author name with relaxed rules for better detection.
    """
    try:
        result = {
            'valid': True,
            'confidence': 0.6,  # Increased base confidence
            'type': 'unknown',
            'suggestions': []
        }
        
        name = author_name.strip()
        
        if not name or len(name) < 2:
            result['valid'] = False
            result['confidence'] = 0.0
            return result
        
        # Check for common name patterns
        words = name.split()
        
        if len(words) == 1:
            # Single word name - more permissive
            if len(words[0]) < 2:
                result['valid'] = False
                result['confidence'] = 0.0
                result['suggestions'].append('Name too short')
            else:
                result['type'] = 'single_name'
                result['confidence'] = 0.5  # Increased from 0.3
                result['suggestions'].append('Consider if this is a complete name')
        
        elif len(words) == 2:
            # Two word name (most common)
            result['type'] = 'first_last'
            result['confidence'] = 0.8
            
            # Check for proper capitalization - more permissive
            if words[0][0].isupper() and words[1][0].isupper():
                result['confidence'] += 0.1
            elif any(word[0].isupper() for word in words):
                result['confidence'] += 0.05  # Partial capitalization is okay
        
        elif len(words) == 3:
            # Three word name (could be first middle last or title first last)
            result['type'] = 'first_middle_last'
            result['confidence'] = 0.7
            
            # Check for academic titles
            academic_titles = ['Dr.', 'Prof.', 'Professor', 'Associate', 'Assistant']
            if words[0] in academic_titles:
                result['type'] = 'title_first_last'
                result['confidence'] += 0.1
        
        elif len(words) > 3:
            # Multiple words (could be with titles, middle names, or affiliations)
            result['type'] = 'complex_name'
            result['confidence'] = 0.6
            
            # Check for academic titles
            academic_titles = ['PhD', 'MD', 'DVM', 'Dr.', 'Professor', 'Prof.']
            for title in academic_titles:
                if title in words:
                    result['confidence'] += 0.05
        
        # More permissive case checking
        if name.isupper():
            result['confidence'] -= 0.05  # Reduced penalty from 0.1
            result['suggestions'].append('Consider proper case formatting')
        
        if name.islower():
            result['confidence'] -= 0.05  # Reduced penalty from 0.1
            result['suggestions'].append('Consider proper case formatting')
        
        # More permissive punctuation checking
        if name.count('.') > 5 or name.count(',') > 4:  # Increased limits
            result['confidence'] -= 0.05  # Reduced penalty from 0.1
            result['suggestions'].append('Consider reducing punctuation')
        
        # More permissive number checking
        if re.search(r'\d', name):
            # Only penalize if it's clearly not a name (e.g., "John123")
            if re.search(r'\d{2,}', name):  # Multiple consecutive digits
                result['confidence'] -= 0.1  # Reduced penalty from 0.2
                result['suggestions'].append('Names typically do not contain numbers')
            else:
                # Single digit might be part of name (e.g., "John 2nd")
                result['confidence'] -= 0.02
        
        # Normalize confidence
        result['confidence'] = max(0.0, min(1.0, result['confidence']))
        
        return result
        
    except Exception as e:
        return {
            'valid': False,
            'confidence': 0.0,
            'type': 'error',
            'suggestions': [f'Validation error: {str(e)}']
        }

def test_author_validation():
    """Test the improved author validation with relaxed rules."""
    print("🔍 Testing Improved Author Validation")
    print("=" * 50)
    
    # Test cases with various author formats
    test_authors = [
        "John Smith",
        "J. Smith",
        "Dr. John Smith",
        "John A. Smith",
        "SMITH, JOHN",  # All caps
        "john smith",   # All lowercase
        "J. A. Smith, PhD",
        "Smith, John and Jones, Mary",
        "John Smith, MD, PhD",
        "A. B. C. D. E. F. G. H.",  # Many initials
        "John123",  # Name with numbers
        "J",  # Too short
        "Dr. John Smith, Professor of Biology, University of Science",
        "Mary Jane Watson",
        "O'Connor, Patrick",
        "van der Berg, Jan",
        "Li Wei",
        "José María García",
        "Jean-Pierre Dubois",
        "Smith",  # Single name
    ]
    
    for author in test_authors:
        result = validate_author_name(author)
        status = "✅" if result['valid'] else "❌"
        confidence = result['confidence']
        print(f"{status} '{author}' -> Valid: {result['valid']}, Confidence: {confidence:.2f}")
        if result['suggestions']:
            print(f"    Suggestions: {', '.join(result['suggestions'])}")

def test_confidence_threshold():
    """Test the new confidence threshold."""
    print("\n🔍 Testing Confidence Threshold")
    print("=" * 50)
    
    # Simulate the new threshold
    threshold = 0.3  # Lowered from 0.7
    print(f"Current Context7 confidence threshold: {threshold}")
    
    if threshold <= 0.4:
        print("✅ Threshold is appropriately low for better detection")
    else:
        print("❌ Threshold might still be too high")

def main():
    """Run all tests."""
    print("🧪 Testing Improved Title and Author Detection")
    print("=" * 60)
    
    try:
        test_author_validation()
        test_confidence_threshold()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        print("\n📋 Summary of Improvements:")
        print("- Relaxed validation rules for better author detection")
        print("- Lowered confidence thresholds from 0.7 to 0.3")
        print("- More permissive font size ranges (10-18pt for authors)")
        print("- Enhanced position-based author detection")
        print("- Better handling of various name formats")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 