#!/usr/bin/env python3
"""
Test Database Vacuum Functionality

This script tests the database vacuum functionality implementation
including the API endpoint, database operations, and error handling.
"""

import os
import sys
import requests
import json
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_health_monitor_import():
    """Test that health monitor can be imported and vacuum function exists."""
    print("Testing health monitor import...")
    
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Check if vacuum_all_databases method exists
        if hasattr(monitor, 'vacuum_all_databases'):
            print("✓ Health monitor imported successfully")
            print("✓ vacuum_all_databases method found")
            return True
        else:
            print("✗ vacuum_all_databases method not found")
            return False
            
    except ImportError as e:
        print(f"✗ Failed to import health monitor: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing health monitor: {e}")
        return False

def test_vacuum_functionality():
    """Test the vacuum functionality directly."""
    print("\nTesting vacuum functionality...")
    
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Test vacuum operation
        print("Starting vacuum operation...")
        results = monitor.vacuum_all_databases()
        
        print(f"✓ Vacuum operation completed")
        print(f"  - Total databases: {results['total_databases']}")
        print(f"  - Successful vacuums: {results['successful_vacuums']}")
        print(f"  - Failed vacuums: {results['failed_vacuums']}")
        print(f"  - Space reclaimed: {results['total_space_reclaimed_mb']:.2f} MB")
        
        if results['details']:
            print("  - Details:")
            for detail in results['details']:
                if detail['success']:
                    print(f"    ✓ {detail['database']}: {detail.get('space_reclaimed_mb', 0):.2f} MB reclaimed")
                else:
                    print(f"    ✗ {detail['database']}: {detail.get('error', 'Unknown error')}")
        
        return results['success']
        
    except Exception as e:
        print(f"✗ Error testing vacuum functionality: {e}")
        return False

def test_database_metrics():
    """Test enhanced database metrics."""
    print("\nTesting enhanced database metrics...")
    
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Get database metrics
        db_metrics = monitor.get_database_metrics()
        
        print(f"✓ Found {len(db_metrics)} databases")
        
        for db in db_metrics:
            print(f"  - {db.database_name} ({db.database_type})")
            print(f"    Size: {db.size_mb:.2f} MB")
            print(f"    Last modified: {db.last_modified}")
            print(f"    Integrity: {'OK' if db.integrity_check else 'Failed'}")
            if db.file_path:
                print(f"    Path: {db.file_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing database metrics: {e}")
        return False

def test_api_endpoint():
    """Test the vacuum API endpoint."""
    print("\nTesting vacuum API endpoint...")
    
    # Note: This would require authentication in a real test
    # For now, we'll just check if the endpoint exists
    
    try:
        # This is a basic test - in reality you'd need to authenticate first
        url = "http://localhost:8080/admin/health/api/vacuum-databases"
        
        # Just check if the endpoint exists (will return 401/403 without auth)
        response = requests.post(url, timeout=5)
        
        if response.status_code in [401, 403]:
            print("✓ API endpoint exists (authentication required)")
            return True
        elif response.status_code == 200:
            print("✓ API endpoint accessible")
            data = response.json()
            print(f"  Response: {data}")
            return True
        else:
            print(f"✗ Unexpected response code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to Flask application")
        print("  Make sure the Flask app is running on localhost:8080")
        return False
    except Exception as e:
        print(f"✗ Error testing API endpoint: {e}")
        return False

def main():
    """Run all tests."""
    print("Database Vacuum Functionality Test")
    print("=" * 40)
    
    tests = [
        ("Health Monitor Import", test_health_monitor_import),
        ("Database Metrics", test_database_metrics),
        ("Vacuum Functionality", test_vacuum_functionality),
        ("API Endpoint", test_api_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("Test Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
