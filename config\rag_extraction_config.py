"""
Configuration settings for RAG-enhanced PDF text extraction.
"""

# RAG Extraction Settings
RAG_EXTRACTION_CONFIG = {
    # Enable/disable RAG extraction
    "enabled": True,
    
    # Context7 integration
    "use_context7": True,  # Enable Context7 enhancement by default
    
    # Column detection settings
    "column_detection": True,
    "column_threshold": 50,  # Distance threshold for column separation
    
    # Sentence orchestration
    "sentence_orchestration": True,
    
    # Table detection strategy
    "table_strategy": "advanced",  # Options: "advanced", "basic", None
    
    # Word extraction
    "extract_words": True,
    
    # Graphics handling
    "ignore_graphics": False,
    "ignore_images": False,
    "graphics_limit": None,  # Set to float value to limit graphics processing
    
    # Image processing
    "dpi": 300,
    
    # Text processing
    "sort_text": True,
    "detect_columns": True,
    
    # Performance settings
    "debug": False,
    "save_text": True,
    
    # Fallback settings
    "fallback_to_standard": True,
    "fallback_to_ocr": True,
    "min_text_threshold": 100,  # Minimum text length for successful extraction
}

# Context7 API Configuration (for future use)
CONTEXT7_CONFIG = {
    "api_key": None,  # Set your Context7 API key here
    "base_url": "https://api.context7.com",
    "timeout": 30,
    "max_retries": 3,
    "enable_semantic_analysis": True,  # Enable semantic analysis
    "enable_content_classification": True,  # Enable content classification
    "enable_structure_improvement": True,  # Enable structure improvement
}

# Context7 Title and Author Extraction Configuration
CONTEXT7_TITLE_AUTHOR_CONFIG = {
    "enabled": True,  # Enable Context7 enhancement for title/author extraction
    "confidence_threshold": 0.3,  # Lowered from 0.7 for better detection
    "title_enhancement": True,  # Enable title enhancement
    "author_enhancement": True,  # Enable author enhancement
    "validation_enabled": True,  # Enable validation checks
    "semantic_analysis": True,  # Enable semantic analysis
    "spaCy_integration": True,  # Enable spaCy person name detection
    "academic_title_detection": True,  # Enable academic title detection
    "affiliation_detection": True,  # Enable institutional affiliation detection
    "context_validation": True,  # Enable PDF context validation
    "fallback_strategy": True,  # Enable fallback to standard methods
    "debug_mode": False,  # Enable debug output for Context7 functions
}

# Advanced Column Detection Settings
COLUMN_DETECTION_CONFIG = {
    "threshold": 50,  # Distance threshold for column separation
    "min_column_width": 100,  # Minimum width for a column
    "max_columns": 4,  # Maximum number of columns to detect
    "sort_columns": True,  # Sort columns left to right
    "sort_text_within_columns": True,  # Sort text within each column
}

# Sentence Flow Improvement Settings
SENTENCE_FLOW_CONFIG = {
    "fix_hyphenation": True,
    "fix_broken_words": True,
    "improve_paragraph_breaks": True,
    "remove_excessive_whitespace": True,
    "max_consecutive_newlines": 2,
}

# Performance Monitoring Settings
PERFORMANCE_CONFIG = {
    "track_memory": True,
    "track_cpu": True,
    "log_parameters": True,
    "log_result_size": True,
}

def get_rag_config():
    """Get the RAG extraction configuration."""
    return RAG_EXTRACTION_CONFIG.copy()

def get_context7_config():
    """Get the Context7 configuration."""
    return CONTEXT7_CONFIG.copy()

def get_column_detection_config():
    """Get the column detection configuration."""
    return COLUMN_DETECTION_CONFIG.copy()

def get_sentence_flow_config():
    """Get the sentence flow configuration."""
    return SENTENCE_FLOW_CONFIG.copy()

def get_performance_config():
    """Get the performance monitoring configuration."""
    return PERFORMANCE_CONFIG.copy()

def update_rag_config(**kwargs):
    """Update RAG extraction configuration with new values."""
    global RAG_EXTRACTION_CONFIG
    RAG_EXTRACTION_CONFIG.update(kwargs)

def update_context7_config(**kwargs):
    """Update Context7 configuration with new values."""
    global CONTEXT7_CONFIG
    CONTEXT7_CONFIG.update(kwargs)

def is_rag_enabled():
    """Check if RAG extraction is enabled."""
    return RAG_EXTRACTION_CONFIG.get("enabled", True)

def is_context7_enabled():
    """Check if Context7 integration is enabled."""
    return RAG_EXTRACTION_CONFIG.get("use_context7", False) and CONTEXT7_CONFIG.get("api_key") 

def get_context7_title_author_config():
    """Get the Context7 title and author extraction configuration."""
    return CONTEXT7_TITLE_AUTHOR_CONFIG.copy()

def update_context7_title_author_config(**kwargs):
    """Update Context7 title and author extraction configuration with new values."""
    global CONTEXT7_TITLE_AUTHOR_CONFIG
    CONTEXT7_TITLE_AUTHOR_CONFIG.update(kwargs)

def is_context7_title_author_enabled():
    """Check if Context7 title and author extraction is enabled."""
    return (CONTEXT7_TITLE_AUTHOR_CONFIG.get("enabled", False) and 
            CONTEXT7_TITLE_AUTHOR_CONFIG.get("title_enhancement", False) or 
            CONTEXT7_TITLE_AUTHOR_CONFIG.get("author_enhancement", False))

def get_context7_confidence_threshold():
    """Get the Context7 confidence threshold for title/author extraction."""
    return CONTEXT7_TITLE_AUTHOR_CONFIG.get("confidence_threshold", 0.7) 