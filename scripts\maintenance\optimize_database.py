#!/usr/bin/env python3
"""
Database Optimization Script for ERDB System

This script performs comprehensive database optimization including:
- Creating missing indexes
- Analyzing query performance
- Running VACUUM and ANALYZE operations
- Optimizing SQLite settings
- Generating optimization reports
"""

import os
import sys
import logging
import json
import time
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.utils.database_optimizer import get_database_optimizer
from app.utils.db_connection import get_connection, db_connection
from app.utils.performance_monitor import get_performance_monitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def optimize_sqlite_settings(db_path: str):
    """Apply optimal SQLite settings for performance."""
    logger.info(f"Optimizing SQLite settings for {db_path}")
    
    optimizations = [
        "PRAGMA journal_mode = WAL",
        "PRAGMA synchronous = NORMAL", 
        "PRAGMA cache_size = 10000",
        "PRAGMA temp_store = MEMORY",
        "PRAGMA mmap_size = 268435456",  # 256MB
        "PRAGMA foreign_keys = ON",
        "PRAGMA busy_timeout = 30000"
    ]
    
    with db_connection(db_path) as conn:
        cursor = conn.cursor()
        
        for pragma in optimizations:
            try:
                cursor.execute(pragma)
                logger.info(f"Applied: {pragma}")
            except Exception as e:
                logger.error(f"Failed to apply {pragma}: {str(e)}")

def run_maintenance_operations(db_path: str):
    """Run database maintenance operations."""
    logger.info(f"Running maintenance operations on {db_path}")
    
    with db_connection(db_path) as conn:
        cursor = conn.cursor()
        
        # Run ANALYZE to update statistics
        logger.info("Running ANALYZE to update query statistics...")
        start_time = time.time()
        cursor.execute("ANALYZE")
        analyze_time = time.time() - start_time
        logger.info(f"ANALYZE completed in {analyze_time:.2f} seconds")
        
        # Run VACUUM to reclaim space and defragment
        logger.info("Running VACUUM to optimize database file...")
        start_time = time.time()
        cursor.execute("VACUUM")
        vacuum_time = time.time() - start_time
        logger.info(f"VACUUM completed in {vacuum_time:.2f} seconds")
        
        # Update database version info if table exists
        try:
            cursor.execute("""
                INSERT OR REPLACE INTO database_version (version, description)
                VALUES (?, ?)
            """, (
                int(datetime.now().timestamp()),
                f"Database optimized on {datetime.now().isoformat()}"
            ))
            conn.commit()
        except Exception as e:
            logger.debug(f"Could not update database_version table: {str(e)}")

def analyze_slow_queries(db_path: str) -> dict:
    """Analyze potentially slow queries and suggest optimizations."""
    logger.info("Analyzing common query patterns for optimization opportunities")
    
    optimizer = get_database_optimizer(db_path)
    
    # Common queries that might be slow
    test_queries = [
        "SELECT * FROM chat_history WHERE category = 'CANOPY' ORDER BY timestamp DESC LIMIT 10",
        "SELECT * FROM chat_history WHERE user_id = 1 AND timestamp > datetime('now', '-7 days')",
        "SELECT COUNT(*) FROM pdf_documents WHERE category = 'MANUAL'",
        "SELECT * FROM source_urls WHERE status = 'active' AND last_scraped < datetime('now', '-1 day')",
        "SELECT u.username, COUNT(ch.id) as chat_count FROM users u LEFT JOIN chat_history ch ON u.user_id = ch.user_id GROUP BY u.user_id",
        "SELECT * FROM pdf_documents pd JOIN source_urls su ON pd.source_url_id = su.id WHERE su.status = 'active'"
    ]
    
    query_analysis = []
    
    for query in test_queries:
        try:
            plan = optimizer.analyze_query_plan(query)
            
            # Determine if query needs optimization
            needs_optimization = (
                plan.table_scans > 0 or 
                not plan.uses_index or 
                plan.estimated_cost > 10
            )
            
            query_analysis.append({
                "query": query[:100] + "..." if len(query) > 100 else query,
                "estimated_cost": plan.estimated_cost,
                "uses_index": plan.uses_index,
                "table_scans": plan.table_scans,
                "index_scans": plan.index_scans,
                "needs_optimization": needs_optimization,
                "plan": plan.plan
            })
            
        except Exception as e:
            logger.error(f"Error analyzing query: {str(e)}")
    
    return {
        "analyzed_queries": query_analysis,
        "total_queries": len(test_queries),
        "queries_needing_optimization": sum(1 for q in query_analysis if q.get("needs_optimization", False))
    }

def generate_optimization_report(db_path: str) -> dict:
    """Generate a comprehensive optimization report."""
    logger.info("Generating optimization report")
    
    optimizer = get_database_optimizer(db_path)
    
    # Get database statistics
    table_stats = optimizer.get_table_statistics()
    existing_indexes = optimizer.analyze_indexes()
    index_suggestions = optimizer.suggest_indexes()
    query_analysis = analyze_slow_queries(db_path)
    
    # Get database file size
    db_size_mb = os.path.getsize(db_path) / (1024 * 1024) if os.path.exists(db_path) else 0
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "database_path": db_path,
        "database_size_mb": round(db_size_mb, 2),
        "table_statistics": [
            {
                "table_name": stat.table_name,
                "row_count": stat.row_count,
                "size_kb": round(stat.size_kb, 2),
                "index_count": stat.index_count
            }
            for stat in table_stats
        ],
        "existing_indexes": [
            {
                "name": idx.name,
                "table": idx.table,
                "columns": idx.columns,
                "unique": idx.unique,
                "size_kb": round(idx.size_kb, 2) if idx.size_kb else None
            }
            for idx in existing_indexes
        ],
        "index_suggestions": index_suggestions,
        "query_analysis": query_analysis,
        "optimization_summary": {
            "total_tables": len(table_stats),
            "total_indexes": len(existing_indexes),
            "suggested_indexes": len(index_suggestions),
            "largest_table": max(table_stats, key=lambda x: x.row_count).table_name if table_stats else None,
            "total_rows": sum(stat.row_count for stat in table_stats)
        }
    }
    
    return report

def main():
    """Main optimization function."""
    logger.info("Starting database optimization process")
    
    # Get database path from environment or use default
    db_path = os.getenv("DB_PATH", "./erdb_main.db")
    
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return 1
    
    try:
        # Generate pre-optimization report
        logger.info("Generating pre-optimization report...")
        pre_report = generate_optimization_report(db_path)
        
        # Apply SQLite optimizations
        optimize_sqlite_settings(db_path)
        
        # Create recommended indexes
        logger.info("Creating recommended indexes...")
        optimizer = get_database_optimizer(db_path)
        index_results = optimizer.create_recommended_indexes()
        
        # Run maintenance operations
        run_maintenance_operations(db_path)
        
        # Generate post-optimization report
        logger.info("Generating post-optimization report...")
        post_report = generate_optimization_report(db_path)
        
        # Save reports
        reports_dir = "./reports"
        os.makedirs(reports_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        with open(f"{reports_dir}/db_optimization_pre_{timestamp}.json", "w") as f:
            json.dump(pre_report, f, indent=2)
        
        with open(f"{reports_dir}/db_optimization_post_{timestamp}.json", "w") as f:
            json.dump(post_report, f, indent=2)
        
        with open(f"{reports_dir}/index_creation_{timestamp}.json", "w") as f:
            json.dump(index_results, f, indent=2)
        
        # Print summary
        logger.info("Database optimization completed successfully!")
        logger.info(f"Created {len([r for r in index_results if r['status'] == 'created'])} new indexes")
        logger.info(f"Database size: {post_report['database_size_mb']:.2f} MB")
        logger.info(f"Total tables: {post_report['optimization_summary']['total_tables']}")
        logger.info(f"Total indexes: {post_report['optimization_summary']['total_indexes']}")
        logger.info(f"Reports saved to {reports_dir}/")
        
        return 0
        
    except Exception as e:
        logger.error(f"Database optimization failed: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
