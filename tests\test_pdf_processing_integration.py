"""
Integration test for the complete PDF processing pipeline with enhanced metadata extraction.

This test verifies that the enhanced metadata extraction works end-to-end
in the actual PDF processing workflow.
"""

import unittest
import tempfile
import os
import sys
from unittest.mock import patch, MagicMock

# Add the parent directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.pdf_processor import process_pdf


class TestPDFProcessingIntegration(unittest.TestCase):
    """Integration test for PDF processing with enhanced metadata."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.test_pdf_path = self.create_test_pdf()
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def create_test_pdf(self):
        """Create a test PDF file for integration testing."""
        try:
            import fitz  # PyMuPDF
            
            # Create a PDF with content and metadata
            doc = fitz.open()
            page = doc.new_page()
            
            # Add some text content
            text = """
            Research Paper Title: Advanced Machine Learning Techniques
            
            Authors: <AUTHORS>
            This paper presents novel approaches to machine learning
            that improve accuracy and performance in various domains.
            
            Keywords: machine learning, artificial intelligence, neural networks
            
            Published: March 2023
            """
            
            page.insert_text((72, 72), text)
            
            # Set PDF metadata
            metadata = {
                'title': 'Advanced Machine Learning Techniques',
                'author': 'Dr. Jane Smith, Prof. John Doe',
                'subject': 'Machine Learning Research',
                'keywords': 'machine learning, AI, neural networks',
                'creator': 'Research Paper Generator',
                'producer': 'Academic Publishing System',
                'creationDate': 'D:20230315143022+00\'00\'',
                'modDate': 'D:20230315143022+00\'00\''
            }
            doc.set_metadata(metadata)
            
            # Save the PDF
            pdf_path = os.path.join(self.test_dir, 'research_paper.pdf')
            doc.save(pdf_path)
            doc.close()
            
            return pdf_path
            
        except ImportError:
            # Create a dummy PDF if PyMuPDF is not available
            pdf_path = os.path.join(self.test_dir, 'research_paper.pdf')
            with open(pdf_path, 'wb') as f:
                f.write(b'%PDF-1.4\n%dummy PDF for integration testing')
            return pdf_path
    
    def test_full_pdf_processing_pipeline(self):
        """Test the complete PDF processing pipeline with enhanced metadata."""
        try:
            # Process the PDF using the main process_pdf function
            result = process_pdf(
                self.test_pdf_path,
                category='test_research',
                source_url='https://example.com/research-paper.pdf',
                extract_tables=False,  # Disable table extraction for simpler test
                save_images=False,     # Disable image extraction for simpler test
                save_tables=False,
                use_vision=False,
                extract_locations=False
            )
            
            # Verify that the result contains the expected structure
            self.assertIsInstance(result, dict)
            self.assertIn('metadata', result)
            
            metadata = result['metadata']
            
            # Check that basic metadata is present
            self.assertIn('filename', metadata)
            self.assertIn('category', metadata)
            self.assertIn('extraction_date', metadata)
            
            # Check that enhanced PDF metadata is present
            self.assertIn('enhanced_pdf_metadata', metadata)
            enhanced_metadata = metadata['enhanced_pdf_metadata']
            
            # If PyMuPDF is available, check actual extracted values
            if enhanced_metadata and enhanced_metadata.get('enhanced_title'):
                self.assertEqual(enhanced_metadata['enhanced_title'], 'Advanced Machine Learning Techniques')
                self.assertEqual(enhanced_metadata['enhanced_author'], 'Dr. Jane Smith, Prof. John Doe')
                self.assertEqual(enhanced_metadata.get('subject'), 'Machine Learning Research')
                self.assertEqual(enhanced_metadata.get('keywords'), 'machine learning, AI, neural networks')
                
                # Check extraction methods
                self.assertEqual(enhanced_metadata.get('title_extraction_method'), 'native_metadata')
                self.assertEqual(enhanced_metadata.get('author_extraction_method'), 'native_metadata')
            
            # Check that top-level metadata fields are populated
            if metadata.get('pdf_title'):
                self.assertEqual(metadata['pdf_title'], 'Advanced Machine Learning Techniques')
            if metadata.get('pdf_author'):
                self.assertEqual(metadata['pdf_author'], 'Dr. Jane Smith, Prof. John Doe')
            
            # Verify that text extraction still works
            self.assertIn('text', result)
            self.assertIsInstance(result['text'], list)
            
            # Verify that other components still work
            self.assertIn('images', result)
            self.assertIn('tables', result)
            self.assertIn('links', result)
            
            print("✓ Full PDF processing pipeline test passed")
            
        except Exception as e:
            # If there are import issues or other problems, just verify structure
            self.fail(f"PDF processing pipeline failed: {str(e)}")
    
    def test_metadata_extraction_fallback_strategies(self):
        """Test that fallback strategies work when native metadata is missing."""
        # Create a PDF without metadata
        try:
            import fitz
            
            doc = fitz.open()
            page = doc.new_page()
            
            # Add content that should be detected by content analysis
            text = """
            Title: Fallback Strategy Test Document
            
            Author: Test Author Name
            
            This document tests the fallback strategies for metadata extraction
            when native PDF metadata is not available.
            """
            
            page.insert_text((72, 72), text)
            
            # Don't set any metadata - let it use fallback strategies
            
            pdf_path = os.path.join(self.test_dir, 'fallback_test.pdf')
            doc.save(pdf_path)
            doc.close()
            
            # Process the PDF
            result = process_pdf(
                pdf_path,
                category='test_fallback',
                extract_tables=False,
                save_images=False,
                save_tables=False,
                use_vision=False,
                extract_locations=False
            )
            
            # Check that metadata was extracted using fallback methods
            metadata = result.get('metadata', {})
            enhanced_metadata = metadata.get('enhanced_pdf_metadata', {})
            
            # Should have extracted title and author using content analysis or filename
            if enhanced_metadata.get('enhanced_title'):
                title_method = enhanced_metadata.get('title_extraction_method')
                self.assertIn(title_method, ['content_analysis', 'ocr_analysis', 'filename'])
            
            if enhanced_metadata.get('enhanced_author'):
                author_method = enhanced_metadata.get('author_extraction_method')
                self.assertIn(author_method, ['content_analysis', 'ocr_analysis'])
            
            print("✓ Metadata extraction fallback strategies test passed")
            
        except ImportError:
            # Skip this test if PyMuPDF is not available
            self.skipTest("PyMuPDF not available for fallback strategy testing")
    
    def test_backward_compatibility(self):
        """Test that the enhanced processing doesn't break existing functionality."""
        try:
            # Process PDF with minimal parameters (old-style call)
            result = process_pdf(self.test_pdf_path)
            
            # Should still work and return expected structure
            self.assertIsInstance(result, dict)
            self.assertIn('text', result)
            self.assertIn('metadata', result)
            self.assertIn('images', result)
            self.assertIn('tables', result)
            self.assertIn('links', result)
            
            # Enhanced metadata should be present but not break anything
            metadata = result['metadata']
            self.assertIn('enhanced_pdf_metadata', metadata)
            
            print("✓ Backward compatibility test passed")
            
        except Exception as e:
            self.fail(f"Backward compatibility test failed: {str(e)}")
    
    def test_error_handling_in_pipeline(self):
        """Test that the pipeline handles errors gracefully."""
        # Test with non-existent file
        result = process_pdf('/nonexistent/file.pdf', category='test_error')
        
        # Should return a result structure even if processing fails
        self.assertIsInstance(result, dict)
        
        # Enhanced metadata should be present but empty/default
        metadata = result.get('metadata', {})
        enhanced_metadata = metadata.get('enhanced_pdf_metadata', {})
        self.assertIsInstance(enhanced_metadata, dict)
        
        print("✓ Error handling test passed")


if __name__ == '__main__':
    # Run the integration tests
    unittest.main(verbosity=2)
