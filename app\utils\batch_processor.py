"""
Optimized Batch Processing System for ERDB

This module provides efficient batch processing capabilities for:
- Vision model operations
- PDF text extraction
- OCR processing
- Vector embedding generation
- Database operations

Features:
- Parallel processing with configurable workers
- Memory-efficient chunking
- Progress tracking and error handling
- Resource usage optimization
- Caching and result reuse
"""

import asyncio
import concurrent.futures
import logging
import time
import os
import psutil
from typing import List, Dict, Any, Callable, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime
import threading
from queue import Queue, Empty
import json

# Import performance monitor with fallback
try:
    from app.utils.performance_monitor import performance_monitor, get_performance_monitor
    HAS_PERFORMANCE_MONITOR = True
except ImportError:
    # Create dummy decorator if performance monitor is not available
    def performance_monitor(**kwargs):
        def decorator(func):
            return func
        return decorator

    def get_performance_monitor():
        return None

    HAS_PERFORMANCE_MONITOR = False

logger = logging.getLogger(__name__)

@dataclass
class BatchJob:
    """Represents a batch processing job."""
    job_id: str
    items: List[Any]
    processor_func: Callable
    kwargs: Dict[str, Any]
    priority: int = 0
    max_workers: Optional[int] = None
    chunk_size: Optional[int] = None

@dataclass
class BatchResult:
    """Result of batch processing."""
    job_id: str
    success: bool
    results: List[Any]
    errors: List[str]
    processing_time: float
    items_processed: int
    items_failed: int

class ResourceMonitor:
    """Monitor system resources during batch processing."""
    
    def __init__(self):
        self.cpu_threshold = float(os.getenv('BATCH_CPU_THRESHOLD', '80.0'))
        self.memory_threshold = float(os.getenv('BATCH_MEMORY_THRESHOLD', '85.0'))
        self.monitoring = False
        self.stats = {
            'cpu_usage': [],
            'memory_usage': [],
            'timestamps': []
        }
    
    def start_monitoring(self):
        """Start resource monitoring."""
        self.monitoring = True
        self.stats = {'cpu_usage': [], 'memory_usage': [], 'timestamps': []}
        
        def monitor_loop():
            while self.monitoring:
                try:
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory_percent = psutil.virtual_memory().percent
                    
                    self.stats['cpu_usage'].append(cpu_percent)
                    self.stats['memory_usage'].append(memory_percent)
                    self.stats['timestamps'].append(time.time())
                    
                    # Keep only last 100 measurements
                    if len(self.stats['cpu_usage']) > 100:
                        for key in self.stats:
                            self.stats[key] = self.stats[key][-100:]
                
                except Exception as e:
                    logger.error(f"Error in resource monitoring: {str(e)}")
                
                time.sleep(1)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop resource monitoring."""
        self.monitoring = False
    
    def should_throttle(self) -> bool:
        """Check if processing should be throttled due to resource usage."""
        if not self.stats['cpu_usage'] or not self.stats['memory_usage']:
            return False
        
        recent_cpu = self.stats['cpu_usage'][-5:]  # Last 5 measurements
        recent_memory = self.stats['memory_usage'][-5:]
        
        avg_cpu = sum(recent_cpu) / len(recent_cpu)
        avg_memory = sum(recent_memory) / len(recent_memory)
        
        return avg_cpu > self.cpu_threshold or avg_memory > self.memory_threshold
    
    def get_optimal_workers(self, max_workers: int) -> int:
        """Get optimal number of workers based on current resource usage."""
        if self.should_throttle():
            return max(1, max_workers // 2)
        return max_workers

class BatchProcessor:
    """Optimized batch processing system."""
    
    def __init__(self):
        self.max_workers = int(os.getenv('BATCH_MAX_WORKERS', str(psutil.cpu_count())))
        self.default_chunk_size = int(os.getenv('BATCH_DEFAULT_CHUNK_SIZE', '10'))
        self.resource_monitor = ResourceMonitor()
        self.job_queue = Queue()
        self.results_cache = {}
        self.cache_enabled = os.getenv('BATCH_CACHE_ENABLED', 'true').lower() == 'true'
        
        # Start resource monitoring
        self.resource_monitor.start_monitoring()
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def process_batch(self, job: BatchJob) -> BatchResult:
        """Process a batch job with optimization."""
        start_time = time.time()
        
        logger.info(f"Starting batch job {job.job_id} with {len(job.items)} items")
        
        # Determine optimal processing parameters
        max_workers = job.max_workers or self.max_workers
        optimal_workers = self.resource_monitor.get_optimal_workers(max_workers)
        chunk_size = job.chunk_size or self._calculate_optimal_chunk_size(len(job.items), optimal_workers)
        
        logger.info(f"Using {optimal_workers} workers with chunk size {chunk_size}")
        
        results = []
        errors = []
        items_processed = 0
        items_failed = 0
        
        try:
            # Process items in chunks
            chunks = self._create_chunks(job.items, chunk_size)
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=optimal_workers) as executor:
                # Submit all chunks for processing
                future_to_chunk = {}
                
                for i, chunk in enumerate(chunks):
                    future = executor.submit(
                        self._process_chunk,
                        chunk,
                        job.processor_func,
                        job.kwargs,
                        f"{job.job_id}_chunk_{i}"
                    )
                    future_to_chunk[future] = i
                
                # Collect results as they complete
                for future in concurrent.futures.as_completed(future_to_chunk):
                    chunk_idx = future_to_chunk[future]
                    
                    try:
                        chunk_results, chunk_errors = future.result()
                        results.extend(chunk_results)
                        errors.extend(chunk_errors)
                        items_processed += len(chunk_results)
                        items_failed += len(chunk_errors)
                        
                        # Log progress
                        progress = (chunk_idx + 1) / len(chunks) * 100
                        logger.info(f"Job {job.job_id} progress: {progress:.1f}%")
                        
                        # Check if we should throttle
                        if self.resource_monitor.should_throttle():
                            logger.warning(f"High resource usage detected, throttling job {job.job_id}")
                            time.sleep(1)
                    
                    except Exception as e:
                        logger.error(f"Chunk {chunk_idx} failed: {str(e)}")
                        errors.append(f"Chunk {chunk_idx}: {str(e)}")
                        items_failed += len(chunks[chunk_idx])
        
        except Exception as e:
            logger.error(f"Batch job {job.job_id} failed: {str(e)}")
            errors.append(f"Job failed: {str(e)}")
        
        processing_time = time.time() - start_time
        
        result = BatchResult(
            job_id=job.job_id,
            success=len(errors) == 0,
            results=results,
            errors=errors,
            processing_time=processing_time,
            items_processed=items_processed,
            items_failed=items_failed
        )
        
        logger.info(f"Batch job {job.job_id} completed in {processing_time:.2f}s: "
                   f"{items_processed} processed, {items_failed} failed")
        
        return result
    
    def _process_chunk(self, chunk: List[Any], processor_func: Callable, 
                      kwargs: Dict[str, Any], chunk_id: str) -> Tuple[List[Any], List[str]]:
        """Process a single chunk of items."""
        results = []
        errors = []
        
        for item in chunk:
            try:
                # Check cache if enabled
                cache_key = None
                if self.cache_enabled:
                    cache_key = self._generate_cache_key(item, processor_func.__name__, kwargs)
                    if cache_key in self.results_cache:
                        results.append(self.results_cache[cache_key])
                        continue
                
                # Process item
                result = processor_func(item, **kwargs)
                results.append(result)
                
                # Cache result if enabled
                if self.cache_enabled and cache_key:
                    self.results_cache[cache_key] = result
                    
                    # Limit cache size
                    if len(self.results_cache) > 1000:
                        # Remove oldest 20% of entries
                        keys_to_remove = list(self.results_cache.keys())[:200]
                        for key in keys_to_remove:
                            del self.results_cache[key]
            
            except Exception as e:
                error_msg = f"Item processing failed: {str(e)}"
                errors.append(error_msg)
                logger.error(f"Error in chunk {chunk_id}: {error_msg}")
        
        return results, errors
    
    def _create_chunks(self, items: List[Any], chunk_size: int) -> List[List[Any]]:
        """Create chunks from items list."""
        chunks = []
        for i in range(0, len(items), chunk_size):
            chunks.append(items[i:i + chunk_size])
        return chunks
    
    def _calculate_optimal_chunk_size(self, total_items: int, num_workers: int) -> int:
        """Calculate optimal chunk size based on items and workers."""
        if total_items <= num_workers:
            return 1
        
        # Aim for 2-3 chunks per worker for good load balancing
        target_chunks = num_workers * 2.5
        optimal_size = max(1, int(total_items / target_chunks))
        
        # Ensure chunk size is reasonable
        return min(optimal_size, self.default_chunk_size * 2)
    
    def _generate_cache_key(self, item: Any, func_name: str, kwargs: Dict[str, Any]) -> str:
        """Generate cache key for an item."""
        import hashlib
        
        # Create a string representation of the item and parameters
        key_data = {
            'item': str(item)[:100],  # Limit item representation
            'function': func_name,
            'kwargs': {k: str(v)[:50] for k, v in kwargs.items()}  # Limit kwargs
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    async def process_batch_async(self, job: BatchJob) -> BatchResult:
        """Process batch job asynchronously."""
        loop = asyncio.get_event_loop()
        
        # Run the synchronous batch processing in a thread pool
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            result = await loop.run_in_executor(executor, self.process_batch, job)
        
        return result
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """Get current resource usage statistics."""
        stats = self.resource_monitor.stats.copy()
        
        if stats['cpu_usage']:
            return {
                'current_cpu': stats['cpu_usage'][-1] if stats['cpu_usage'] else 0,
                'avg_cpu': sum(stats['cpu_usage']) / len(stats['cpu_usage']),
                'max_cpu': max(stats['cpu_usage']),
                'current_memory': stats['memory_usage'][-1] if stats['memory_usage'] else 0,
                'avg_memory': sum(stats['memory_usage']) / len(stats['memory_usage']),
                'max_memory': max(stats['memory_usage']),
                'should_throttle': self.resource_monitor.should_throttle(),
                'optimal_workers': self.resource_monitor.get_optimal_workers(self.max_workers)
            }
        
        return {
            'current_cpu': 0,
            'avg_cpu': 0,
            'max_cpu': 0,
            'current_memory': 0,
            'avg_memory': 0,
            'max_memory': 0,
            'should_throttle': False,
            'optimal_workers': self.max_workers
        }

# Global batch processor instance
_batch_processor: Optional[BatchProcessor] = None

def get_batch_processor() -> BatchProcessor:
    """Get the global batch processor instance."""
    global _batch_processor
    if _batch_processor is None:
        _batch_processor = BatchProcessor()
    return _batch_processor

# Convenience functions for common batch operations
def batch_process_images(images: List[str], processor_func: Callable, 
                        max_workers: Optional[int] = None, **kwargs) -> BatchResult:
    """Batch process images with optimization."""
    processor = get_batch_processor()
    
    job = BatchJob(
        job_id=f"images_{int(time.time())}",
        items=images,
        processor_func=processor_func,
        kwargs=kwargs,
        max_workers=max_workers
    )
    
    return processor.process_batch(job)

def batch_process_documents(documents: List[Any], processor_func: Callable,
                           max_workers: Optional[int] = None, **kwargs) -> BatchResult:
    """Batch process documents with optimization."""
    processor = get_batch_processor()
    
    job = BatchJob(
        job_id=f"documents_{int(time.time())}",
        items=documents,
        processor_func=processor_func,
        kwargs=kwargs,
        max_workers=max_workers
    )
    
    return processor.process_batch(job)

def batch_generate_embeddings(texts: List[str], embedding_func: Callable,
                             max_workers: Optional[int] = None, **kwargs) -> BatchResult:
    """Batch generate embeddings with optimization."""
    processor = get_batch_processor()
    
    # For embeddings, use smaller chunks to manage memory
    chunk_size = min(5, len(texts) // (max_workers or processor.max_workers) + 1)
    
    job = BatchJob(
        job_id=f"embeddings_{int(time.time())}",
        items=texts,
        processor_func=embedding_func,
        kwargs=kwargs,
        max_workers=max_workers,
        chunk_size=chunk_size
    )
    
    return processor.process_batch(job)
