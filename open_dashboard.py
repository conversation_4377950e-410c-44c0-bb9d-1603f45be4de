#!/usr/bin/env python3
"""
Open ERDB Performance Monitoring Dashboard

This script opens the integrated performance monitoring dashboard in your default browser.
Make sure your Flask application is running before executing this script.
"""

import webbrowser
import os

# Get Flask configuration
flask_host = os.getenv('FLASK_HOST', 'localhost')
flask_port = os.getenv('FLASK_PORT', '8080')

# Handle different host configurations
if flask_host == '0.0.0.0':
    flask_host = 'localhost'

# Build the dashboard URL (integrated with admin interface)
base_url = f"http://{flask_host}:{flask_port}"
dashboard_url = f"{base_url}/admin/health"

print(f"Opening integrated performance dashboard: {dashboard_url}")
print("Note: Performance monitoring is now integrated with the admin health dashboard")
webbrowser.open(dashboard_url)
