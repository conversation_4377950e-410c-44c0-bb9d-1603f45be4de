#!/usr/bin/env python3
"""
Test runner for enhanced PDF metadata extraction functionality.

This script runs the comprehensive test suite for the enhanced PDF metadata
extraction features, including unit tests, integration tests, and edge cases.

Usage:
    python tests/run_metadata_tests.py
    
    # Run specific test class:
    python tests/run_metadata_tests.py TestPDFMetadataExtraction
    
    # Run with verbose output:
    python tests/run_metadata_tests.py -v
"""

import sys
import os
import unittest
import logging

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def run_tests(test_class=None, verbose=False):
    """
    Run the enhanced PDF metadata tests.
    
    Args:
        test_class: Specific test class to run (optional)
        verbose: Whether to run tests in verbose mode
    """
    # Import test modules
    try:
        from test_enhanced_pdf_metadata import (
            TestPDFMetadataExtraction,
            TestDatabaseIntegration,
            TestEdgeCasesAndErrorHandling,
            TestPerformanceAndBackwardCompatibility
        )
    except ImportError as e:
        print(f"Error importing test modules: {e}")
        print("Make sure you're running this from the project root directory.")
        return False
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    if test_class:
        # Run specific test class
        if test_class == 'TestPDFMetadataExtraction':
            suite.addTests(loader.loadTestsFromTestCase(TestPDFMetadataExtraction))
        elif test_class == 'TestDatabaseIntegration':
            suite.addTests(loader.loadTestsFromTestCase(TestDatabaseIntegration))
        elif test_class == 'TestEdgeCasesAndErrorHandling':
            suite.addTests(loader.loadTestsFromTestCase(TestEdgeCasesAndErrorHandling))
        elif test_class == 'TestPerformanceAndBackwardCompatibility':
            suite.addTests(loader.loadTestsFromTestCase(TestPerformanceAndBackwardCompatibility))
        else:
            print(f"Unknown test class: {test_class}")
            return False
    else:
        # Run all test classes
        suite.addTests(loader.loadTestsFromTestCase(TestPDFMetadataExtraction))
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestEdgeCasesAndErrorHandling))
        suite.addTests(loader.loadTestsFromTestCase(TestPerformanceAndBackwardCompatibility))
    
    # Run tests
    verbosity = 2 if verbose else 1
    runner = unittest.TextTestRunner(verbosity=verbosity)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    return len(result.failures) == 0 and len(result.errors) == 0

def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []
    
    try:
        import fitz  # PyMuPDF
    except ImportError:
        missing_deps.append('PyMuPDF (fitz)')
    
    try:
        import sqlite3
    except ImportError:
        missing_deps.append('sqlite3')
    
    if missing_deps:
        print("Warning: Some dependencies are missing:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("Some tests may be skipped or fail.")
        return False
    
    return True

def main():
    """Main entry point for test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run enhanced PDF metadata extraction tests')
    parser.add_argument('test_class', nargs='?', help='Specific test class to run')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--check-deps', action='store_true', help='Check dependencies only')
    
    args = parser.parse_args()
    
    if args.check_deps:
        if check_dependencies():
            print("All dependencies are available.")
            return 0
        else:
            return 1
    
    print("Enhanced PDF Metadata Extraction Test Suite")
    print("=" * 50)
    
    # Check dependencies
    check_dependencies()
    
    # Run tests
    success = run_tests(args.test_class, args.verbose)
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
