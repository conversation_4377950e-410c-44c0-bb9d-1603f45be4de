#!/usr/bin/env python3
"""
Test script for enhanced PDF processing capabilities.
This script tests the new PyMuPDF and spaCy integrations.
"""

import sys
import os
import time

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_imports():
    """Test if all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        # Test basic imports
        import fitz
        print("✅ PyMuPDF (fitz) imported successfully")
    except ImportError as e:
        print(f"❌ PyMuPDF import failed: {e}")
        return False
    
    try:
        import spacy
        print("✅ spaCy imported successfully")
    except ImportError as e:
        print(f"❌ spaCy import failed: {e}")
        return False
    
    return True

def test_spacy_enhancements():
    """Test enhanced spaCy functionality."""
    print("\n🧠 Testing spaCy enhancements...")
    
    try:
        import spacy
        from spacy.language import Language
        
        # Test if we can load the model
        try:
            nlp = spacy.load("en_core_web_sm")
            print("✅ spaCy en_core_web_sm model loaded")
        except OSError:
            print("❌ spaCy en_core_web_sm model not found")
            return False
        
        # Test custom component creation
        @Language.component("test_component")
        def test_component(doc):
            return doc
        
        # Test if we can add components
        if "test_component" not in nlp.pipe_names:
            nlp.add_pipe("test_component")
            print("✅ Custom spaCy component creation works")
        
        # Test basic NER
        doc = nlp("Dr. John Smith works at Stanford University")
        persons = [ent for ent in doc.ents if ent.label_ == "PERSON"]
        orgs = [ent for ent in doc.ents if ent.label_ == "ORG"]
        
        print(f"✅ NER test: Found {len(persons)} persons, {len(orgs)} organizations")
        
        return True
        
    except Exception as e:
        print(f"❌ spaCy enhancement test failed: {e}")
        return False

def test_pymupdf_enhancements():
    """Test enhanced PyMuPDF functionality."""
    print("\n📚 Testing PyMuPDF enhancements...")
    
    try:
        import fitz
        
        # Test if we can create a simple PDF for testing
        doc = fitz.open()  # Create empty document
        page = doc.new_page()
        
        # Add some text
        text_rect = fitz.Rect(50, 50, 200, 100)
        page.insert_text(text_rect.tl, "Test Title", fontsize=16)
        page.insert_text((50, 120), "Dr. John Smith", fontsize=12)
        
        print("✅ PyMuPDF document creation works")
        
        # Test text extraction methods
        try:
            dict_result = page.get_text("dict")
            print("✅ PyMuPDF dict extraction works")
        except Exception as e:
            print(f"❌ PyMuPDF dict extraction failed: {e}")
        
        try:
            rawdict_result = page.get_text("rawdict")
            print("✅ PyMuPDF rawdict extraction works")
        except Exception as e:
            print(f"⚠️  PyMuPDF rawdict extraction not available: {e}")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"❌ PyMuPDF enhancement test failed: {e}")
        return False

def test_enhanced_functions():
    """Test if enhanced functions are properly defined."""
    print("\n🔧 Testing enhanced function definitions...")
    
    # List of enhanced functions that should be available
    enhanced_functions = [
        'extract_enhanced_text_with_positioning',
        'detect_multi_column_layout',
        'enhanced_author_extraction_with_spacy',
        'enhanced_title_detection_with_font_analysis',
        'extract_title_with_enhanced_pymupdf',
        'extract_author_with_enhanced_spacy',
        'calculate_person_entity_confidence',
        'has_academic_title',
        'batch_person_detection',
        'cluster_text_by_position',
        'test_enhanced_pdf_processing'
    ]
    
    try:
        # Import the pdf_processor module
        from services.pdf_processor import *
        
        defined_functions = []
        missing_functions = []
        
        for func_name in enhanced_functions:
            if func_name in globals():
                defined_functions.append(func_name)
                print(f"✅ {func_name}")
            else:
                missing_functions.append(func_name)
                print(f"❌ {func_name}")
        
        print(f"\n📊 Summary: {len(defined_functions)}/{len(enhanced_functions)} functions defined")
        
        if missing_functions:
            print(f"Missing functions: {missing_functions}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Function definition test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Enhanced PDF Processing Test Suite")
    print("=" * 50)
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        ("Import Test", test_imports),
        ("spaCy Enhancements", test_spacy_enhancements),
        ("PyMuPDF Enhancements", test_pymupdf_enhancements),
        ("Enhanced Functions", test_enhanced_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n{'='*50}")
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    print(f"⏱️  Total time: {duration:.2f} seconds")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced PDF processing is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
