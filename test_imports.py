#!/usr/bin/env python3
"""
Test script to verify that the typing imports are working correctly
without triggering Flask dependencies.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_typing_imports():
    """Test that typing imports work correctly."""
    try:
        # Test the typing imports directly
        from typing import List, Dict, Any
        print("✓ Basic typing imports successful")
        
        # Test that we can use List in function signatures
        def test_function(items: List[str]) -> Dict[str, Any]:
            return {"count": len(items)}
        
        result = test_function(["a", "b", "c"])
        assert result["count"] == 3
        print("✓ List type hint usage successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Typing imports failed: {str(e)}")
        return False

def test_performance_monitor_imports():
    """Test performance monitor imports without app dependencies."""
    try:
        # Import the performance monitor module directly
        import importlib.util
        
        # Load the performance monitor module without going through app package
        spec = importlib.util.spec_from_file_location(
            "performance_monitor", 
            "app/utils/performance_monitor.py"
        )
        performance_monitor = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(performance_monitor)
        
        print("✓ Performance monitor module loaded successfully")
        
        # Test that we can access the decorator
        decorator = performance_monitor.performance_monitor
        print("✓ Performance monitor decorator accessible")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance monitor import failed: {str(e)}")
        return False

def test_batch_processor_imports():
    """Test batch processor imports."""
    try:
        import importlib.util
        
        # Load the batch processor module
        spec = importlib.util.spec_from_file_location(
            "batch_processor", 
            "app/utils/batch_processor.py"
        )
        batch_processor = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(batch_processor)
        
        print("✓ Batch processor module loaded successfully")
        
        # Test that we can access the functions
        get_batch_processor = batch_processor.get_batch_processor
        BatchJob = batch_processor.BatchJob
        batch_process_images = batch_processor.batch_process_images
        
        print("✓ Batch processor functions accessible")
        
        return True
        
    except Exception as e:
        print(f"✗ Batch processor import failed: {str(e)}")
        return False

def main():
    """Run all import tests."""
    print("Testing imports for ERDB Performance Monitoring System")
    print("=" * 60)
    
    all_passed = True
    
    # Test basic typing imports
    if not test_typing_imports():
        all_passed = False
    
    # Test performance monitor imports
    if not test_performance_monitor_imports():
        all_passed = False
    
    # Test batch processor imports
    if not test_batch_processor_imports():
        all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("✓ All import tests passed!")
        print("\nThe typing import issue has been resolved.")
        print("The original error was due to missing 'from typing import List, Dict, Any'")
        print("This has been added to the necessary files.")
        print("\nTo run the application, you'll need to install the Flask dependencies:")
        print("pip install flask flask-sqlalchemy")
        return 0
    else:
        print("✗ Some import tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
