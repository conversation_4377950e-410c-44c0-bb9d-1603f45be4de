#!/usr/bin/env python3
"""
Test Admin Dashboard Integration

This script tests the integration of performance monitoring with the admin dashboard
and verifies that all components work correctly with the configured port.
"""

import os
import sys
import requests
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def get_app_config():
    """Get application configuration."""
    return {
        'host': os.getenv('FLASK_HOST', 'localhost'),
        'port': int(os.getenv('FLASK_PORT', '8080')),
        'base_url': f"http://{os.getenv('FLASK_HOST', 'localhost')}:{os.getenv('FLASK_PORT', '8080')}"
    }

def test_admin_dashboard_access():
    """Test that the admin dashboard is accessible."""
    config = get_app_config()
    base_url = config['base_url']
    
    print(f"Testing admin dashboard access at {base_url}")
    
    # Test main admin dashboard
    try:
        response = requests.get(f"{base_url}/admin/dashboard", timeout=5)
        if response.status_code == 200:
            print("✓ Admin dashboard accessible")
            return True
        elif response.status_code == 302:
            print("✓ Admin dashboard accessible (redirected to login)")
            return True
        else:
            print(f"✗ Admin dashboard returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Admin dashboard not accessible: {str(e)}")
        return False

def test_health_dashboard_access():
    """Test that the enhanced health dashboard is accessible."""
    config = get_app_config()
    base_url = config['base_url']
    
    print(f"Testing enhanced health dashboard at {base_url}/admin/health")
    
    try:
        response = requests.get(f"{base_url}/admin/health", timeout=5)
        if response.status_code == 200:
            print("✓ Enhanced health dashboard accessible")
            return True
        elif response.status_code == 302:
            print("✓ Enhanced health dashboard accessible (redirected to login)")
            return True
        else:
            print(f"✗ Health dashboard returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Health dashboard not accessible: {str(e)}")
        return False

def test_performance_api_endpoints():
    """Test that performance API endpoints are available."""
    config = get_app_config()
    base_url = config['base_url']
    
    endpoints = [
        '/admin/health/api/metrics',
        '/admin/health/api/performance-metrics',
        '/admin/health/api/database-optimization',
        '/admin/health/api/batch-status'
    ]
    
    print("Testing performance API endpoints...")
    
    results = {}
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code in [200, 302, 401, 403]:
                print(f"✓ {endpoint} - Available")
                results[endpoint] = True
            else:
                print(f"✗ {endpoint} - Status {response.status_code}")
                results[endpoint] = False
        except requests.exceptions.RequestException as e:
            print(f"✗ {endpoint} - Error: {str(e)}")
            results[endpoint] = False
    
    return results

def test_configuration():
    """Test configuration settings."""
    print("Testing configuration...")
    
    config = get_app_config()
    
    # Check port configuration
    if config['port'] == 8080:
        print("✓ Port configured correctly (8080)")
    elif config['port'] == 5000:
        print("⚠ Port is 5000 (default Flask port) - consider using 8080")
    else:
        print(f"ℹ Port configured to {config['port']}")
    
    # Check environment variables
    env_vars = [
        'PERFORMANCE_MONITORING_ENABLED',
        'PERFORMANCE_DASHBOARD_INTEGRATED',
        'DATABASE_OPTIMIZATION_ENABLED',
        'BATCH_PROCESSING_ENABLED',
        'HEALTH_MONITORING_ENABLED'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'not set')
        if value.lower() == 'true':
            print(f"✓ {var}: {value}")
        else:
            print(f"⚠ {var}: {value}")
    
    return True

def test_performance_monitoring_imports():
    """Test that performance monitoring modules can be imported."""
    print("Testing performance monitoring imports...")
    
    modules = [
        ('app.utils.performance_monitor', 'get_performance_monitor'),
        ('app.utils.health_monitor', 'get_health_monitor'),
        ('app.utils.database_optimizer', 'get_database_optimizer'),
        ('app.utils.batch_processor', 'get_batch_processor'),
        ('app.utils.performance_logger', 'get_performance_logger')
    ]
    
    results = {}
    for module_name, function_name in modules:
        try:
            module = __import__(module_name, fromlist=[function_name])
            func = getattr(module, function_name)
            instance = func()
            print(f"✓ {module_name} - Available")
            results[module_name] = True
        except ImportError as e:
            print(f"✗ {module_name} - Import error: {str(e)}")
            results[module_name] = False
        except Exception as e:
            print(f"⚠ {module_name} - Available but error: {str(e)}")
            results[module_name] = True
    
    return results

def check_flask_app_running():
    """Check if Flask application is running."""
    config = get_app_config()
    base_url = config['base_url']
    
    print(f"Checking if Flask app is running at {base_url}")
    
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✓ Flask application is running (status: {response.status_code})")
        return True
    except requests.exceptions.RequestException:
        print(f"✗ Flask application is not running at {base_url}")
        return False

def print_integration_summary():
    """Print integration summary and instructions."""
    config = get_app_config()
    
    print("\n" + "="*60)
    print("ADMIN DASHBOARD INTEGRATION SUMMARY")
    print("="*60)
    print(f"Application URL: {config['base_url']}")
    print(f"Admin Dashboard: {config['base_url']}/admin/dashboard")
    print(f"Enhanced Health Dashboard: {config['base_url']}/admin/health")
    print(f"Performance API Base: {config['base_url']}/admin/health/api")
    print("="*60)
    
    print("\nIntegration Features:")
    print("✓ Performance monitoring integrated with admin dashboard")
    print("✓ Real-time system health monitoring")
    print("✓ Database optimization tools")
    print("✓ Batch processing status monitoring")
    print("✓ Performance alerts and bottleneck detection")
    print("✓ One-click database optimization")
    print("✓ Auto-refreshing metrics display")
    
    print("\nTo access the integrated dashboard:")
    print("1. Start your Flask application")
    print("2. Navigate to the admin interface")
    print("3. Go to System Health section")
    print(f"4. Or directly access: {config['base_url']}/admin/health")

def main():
    """Main test function."""
    print("ERDB Admin Dashboard Integration Test")
    print("Testing performance monitoring integration with admin dashboard")
    print("="*60)
    
    # Test configuration
    config_ok = test_configuration()
    
    # Test imports
    imports_ok = test_performance_monitoring_imports()
    
    # Check if Flask app is running
    app_running = check_flask_app_running()
    
    if app_running:
        # Test dashboard access
        admin_ok = test_admin_dashboard_access()
        health_ok = test_health_dashboard_access()
        api_ok = test_performance_api_endpoints()
        
        print("\n" + "="*60)
        print("TEST RESULTS SUMMARY")
        print("="*60)
        print(f"Configuration: {'✓ OK' if config_ok else '✗ Issues'}")
        print(f"Module Imports: {'✓ OK' if all(imports_ok.values()) else '⚠ Some issues'}")
        print(f"Flask Application: {'✓ Running' if app_running else '✗ Not running'}")
        print(f"Admin Dashboard: {'✓ Accessible' if admin_ok else '✗ Not accessible'}")
        print(f"Health Dashboard: {'✓ Accessible' if health_ok else '✗ Not accessible'}")
        print(f"API Endpoints: {'✓ Available' if all(api_ok.values()) else '⚠ Some issues'}")
        
        if all([config_ok, app_running, admin_ok, health_ok]):
            print("\n🎉 Integration test PASSED!")
            print("The performance monitoring system is successfully integrated with your admin dashboard.")
        else:
            print("\n⚠ Integration test completed with some issues.")
            print("Check the details above and resolve any problems.")
    else:
        print("\n⚠ Flask application is not running.")
        print("Start your Flask application and run this test again.")
    
    print_integration_summary()
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
