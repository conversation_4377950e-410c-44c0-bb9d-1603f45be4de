"""
Database Query Optimization Utilities for ERDB System

This module provides database optimization tools including:
- Index analysis and creation
- Query performance monitoring
- Database maintenance operations
- Connection pool optimization
- Query plan analysis
"""

import sqlite3
import logging
import time
import os
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import hashlib

from app.utils.db_connection import get_connection, db_connection
from app.utils.performance_monitor import DatabaseMetric, get_performance_monitor

logger = logging.getLogger(__name__)

@dataclass
class IndexInfo:
    """Information about a database index."""
    name: str
    table: str
    columns: List[str]
    unique: bool
    partial: bool
    size_kb: Optional[float] = None

@dataclass
class QueryPlan:
    """Query execution plan information."""
    query_hash: str
    query: str
    plan: List[Dict[str, Any]]
    estimated_cost: float
    uses_index: bool
    table_scans: int
    index_scans: int

@dataclass
class TableStats:
    """Table statistics for optimization."""
    table_name: str
    row_count: int
    size_kb: float
    index_count: int
    last_analyzed: Optional[str]

class DatabaseOptimizer:
    """Database optimization and analysis tool."""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or os.getenv("DB_PATH", "./erdb_main.db")
        self.query_plans: Dict[str, QueryPlan] = {}
        
    def analyze_indexes(self) -> List[IndexInfo]:
        """Analyze existing indexes in the database."""
        indexes = []
        
        with db_connection(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get all indexes
            cursor.execute("""
                SELECT name, tbl_name, sql 
                FROM sqlite_master 
                WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
                ORDER BY tbl_name, name
            """)
            
            for row in cursor.fetchall():
                index_name = row['name']
                table_name = row['tbl_name']
                sql = row['sql']
                
                # Parse index information
                columns = self._parse_index_columns(sql)
                unique = 'UNIQUE' in sql.upper() if sql else False
                partial = 'WHERE' in sql.upper() if sql else False
                
                # Get index size (approximate)
                size_kb = self._get_index_size(conn, index_name)
                
                indexes.append(IndexInfo(
                    name=index_name,
                    table=table_name,
                    columns=columns,
                    unique=unique,
                    partial=partial,
                    size_kb=size_kb
                ))
        
        return indexes
    
    def get_table_statistics(self) -> List[TableStats]:
        """Get statistics for all tables."""
        stats = []
        
        with db_connection(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type = 'table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            
            tables = [row['name'] for row in cursor.fetchall()]
            
            for table in tables:
                try:
                    # Get row count
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    row_count = cursor.fetchone()['count']
                    
                    # Get table size (approximate)
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()
                    
                    # Estimate size based on page count
                    cursor.execute("PRAGMA page_size")
                    page_size = cursor.fetchone()[0]
                    
                    # Get index count
                    cursor.execute("""
                        SELECT COUNT(*) as count FROM sqlite_master 
                        WHERE type = 'index' AND tbl_name = ? AND name NOT LIKE 'sqlite_%'
                    """, (table,))
                    index_count = cursor.fetchone()['count']
                    
                    # Rough size estimation
                    avg_row_size = len(columns) * 20  # Rough estimate
                    size_kb = (row_count * avg_row_size) / 1024
                    
                    stats.append(TableStats(
                        table_name=table,
                        row_count=row_count,
                        size_kb=size_kb,
                        index_count=index_count,
                        last_analyzed=datetime.now().isoformat()
                    ))
                    
                except Exception as e:
                    logger.error(f"Error getting stats for table {table}: {str(e)}")
        
        return stats
    
    def analyze_query_plan(self, query: str) -> QueryPlan:
        """Analyze the execution plan for a query."""
        query_hash = hashlib.md5(query.encode()).hexdigest()[:16]
        
        with db_connection(self.db_path) as conn:
            cursor = conn.cursor()
            
            try:
                # Get query plan
                cursor.execute(f"EXPLAIN QUERY PLAN {query}")
                plan_rows = cursor.fetchall()
                
                plan = [dict(row) for row in plan_rows]
                
                # Analyze plan for optimization opportunities
                uses_index = any('USING INDEX' in str(row.get('detail', '')) for row in plan)
                table_scans = sum(1 for row in plan if 'SCAN TABLE' in str(row.get('detail', '')))
                index_scans = sum(1 for row in plan if 'SEARCH TABLE' in str(row.get('detail', '')))
                
                # Rough cost estimation based on plan complexity
                estimated_cost = len(plan) + (table_scans * 10) + (index_scans * 2)
                
                query_plan = QueryPlan(
                    query_hash=query_hash,
                    query=query,
                    plan=plan,
                    estimated_cost=estimated_cost,
                    uses_index=uses_index,
                    table_scans=table_scans,
                    index_scans=index_scans
                )
                
                self.query_plans[query_hash] = query_plan
                return query_plan
                
            except Exception as e:
                logger.error(f"Error analyzing query plan: {str(e)}")
                return QueryPlan(
                    query_hash=query_hash,
                    query=query,
                    plan=[],
                    estimated_cost=999,
                    uses_index=False,
                    table_scans=1,
                    index_scans=0
                )
    
    def suggest_indexes(self) -> List[Dict[str, Any]]:
        """Suggest indexes based on query patterns and table statistics."""
        suggestions = []
        
        # Common index suggestions based on ERDB schema
        common_suggestions = [
            {
                "table": "chat_history",
                "columns": ["category"],
                "reason": "Frequently filtered by category in queries",
                "sql": "CREATE INDEX IF NOT EXISTS idx_chat_history_category ON chat_history(category)"
            },
            {
                "table": "chat_history", 
                "columns": ["timestamp"],
                "reason": "Used for time-based queries and sorting",
                "sql": "CREATE INDEX IF NOT EXISTS idx_chat_history_timestamp ON chat_history(timestamp)"
            },
            {
                "table": "chat_history",
                "columns": ["user_id", "timestamp"],
                "reason": "Composite index for user-specific time queries",
                "sql": "CREATE INDEX IF NOT EXISTS idx_chat_history_user_time ON chat_history(user_id, timestamp)"
            },
            {
                "table": "pdf_documents",
                "columns": ["category"],
                "reason": "Frequently filtered by category",
                "sql": "CREATE INDEX IF NOT EXISTS idx_pdf_documents_category ON pdf_documents(category)"
            },
            {
                "table": "pdf_documents",
                "columns": ["upload_date"],
                "reason": "Used for date-based queries",
                "sql": "CREATE INDEX IF NOT EXISTS idx_pdf_documents_upload_date ON pdf_documents(upload_date)"
            },
            {
                "table": "source_urls",
                "columns": ["status"],
                "reason": "Frequently filtered by status",
                "sql": "CREATE INDEX IF NOT EXISTS idx_source_urls_status ON source_urls(status)"
            },
            {
                "table": "source_urls",
                "columns": ["last_scraped"],
                "reason": "Used for maintenance and update queries",
                "sql": "CREATE INDEX IF NOT EXISTS idx_source_urls_last_scraped ON source_urls(last_scraped)"
            },
            {
                "table": "users",
                "columns": ["email"],
                "reason": "Used for login and user lookup",
                "sql": "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"
            },
            {
                "table": "users",
                "columns": ["account_status"],
                "reason": "Frequently filtered by account status",
                "sql": "CREATE INDEX IF NOT EXISTS idx_users_account_status ON users(account_status)"
            }
        ]
        
        # Check which indexes already exist
        existing_indexes = self.analyze_indexes()
        existing_index_keys = set()
        
        for idx in existing_indexes:
            key = f"{idx.table}:{':'.join(idx.columns)}"
            existing_index_keys.add(key)
        
        # Filter out existing indexes
        for suggestion in common_suggestions:
            key = f"{suggestion['table']}:{':'.join(suggestion['columns'])}"
            if key not in existing_index_keys:
                suggestions.append(suggestion)
        
        return suggestions
    
    def create_recommended_indexes(self) -> List[Dict[str, Any]]:
        """Create recommended indexes that don't already exist."""
        suggestions = self.suggest_indexes()
        results = []
        
        with db_connection(self.db_path) as conn:
            cursor = conn.cursor()
            
            for suggestion in suggestions:
                try:
                    start_time = time.time()
                    cursor.execute(suggestion['sql'])
                    conn.commit()
                    execution_time = time.time() - start_time
                    
                    results.append({
                        "table": suggestion['table'],
                        "columns": suggestion['columns'],
                        "sql": suggestion['sql'],
                        "status": "created",
                        "execution_time": execution_time,
                        "reason": suggestion['reason']
                    })
                    
                    logger.info(f"Created index on {suggestion['table']}({', '.join(suggestion['columns'])}) in {execution_time:.3f}s")
                    
                except Exception as e:
                    results.append({
                        "table": suggestion['table'],
                        "columns": suggestion['columns'],
                        "sql": suggestion['sql'],
                        "status": "failed",
                        "error": str(e),
                        "reason": suggestion['reason']
                    })
                    
                    logger.error(f"Failed to create index on {suggestion['table']}: {str(e)}")
        
        return results
    
    def _parse_index_columns(self, sql: str) -> List[str]:
        """Parse column names from CREATE INDEX SQL."""
        if not sql:
            return []
        
        try:
            # Extract column list from SQL
            import re
            match = re.search(r'\((.*?)\)', sql)
            if match:
                columns_str = match.group(1)
                # Split by comma and clean up
                columns = [col.strip().strip('"\'') for col in columns_str.split(',')]
                return columns
        except Exception as e:
            logger.error(f"Error parsing index columns from SQL: {str(e)}")
        
        return []
    
    def _get_index_size(self, conn: sqlite3.Connection, index_name: str) -> Optional[float]:
        """Get approximate size of an index in KB."""
        try:
            cursor = conn.cursor()
            # This is a rough estimation - SQLite doesn't provide exact index sizes
            cursor.execute("PRAGMA page_count")
            total_pages = cursor.fetchone()[0]
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            
            # Very rough estimate - assume index takes 5-10% of total DB size
            total_size_kb = (total_pages * page_size) / 1024
            estimated_index_size = total_size_kb * 0.05  # 5% estimate
            
            return estimated_index_size
        except Exception:
            return None

# Global optimizer instance
_db_optimizer: Optional[DatabaseOptimizer] = None

def get_database_optimizer(db_path: str = None) -> DatabaseOptimizer:
    """Get the global database optimizer instance."""
    global _db_optimizer
    if _db_optimizer is None or (db_path and _db_optimizer.db_path != db_path):
        _db_optimizer = DatabaseOptimizer(db_path)
    return _db_optimizer
