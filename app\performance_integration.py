"""
Performance Monitoring Integration for Flask Application

This module provides easy integration of the performance monitoring system
with the Flask application. Import and call setup_performance_monitoring()
in your main Flask app to enable all monitoring features.
"""

import logging
import os
from typing import Optional

logger = logging.getLogger(__name__)

def setup_performance_monitoring(app=None):
    """
    Setup performance monitoring for the Flask application.
    
    Args:
        app: Flask application instance (optional)
        
    Returns:
        Dictionary with setup status and components
    """
    setup_status = {
        'performance_monitor': False,
        'health_monitor': False,
        'performance_logger': False,
        'batch_processor': False,
        'database_optimizer': False,
        'chroma_performance': False,
        'performance_routes': False,
        'errors': []
    }
    
    # Initialize performance monitor
    try:
        from app.utils.performance_monitor import get_performance_monitor
        perf_monitor = get_performance_monitor()
        setup_status['performance_monitor'] = True
        logger.info("Performance monitor initialized")
    except Exception as e:
        setup_status['errors'].append(f"Performance monitor: {str(e)}")
        logger.error(f"Failed to initialize performance monitor: {str(e)}")
    
    # Initialize health monitor
    try:
        from app.utils.health_monitor import get_health_monitor
        health_monitor = get_health_monitor()
        setup_status['health_monitor'] = True
        logger.info("Health monitor initialized")
    except Exception as e:
        setup_status['errors'].append(f"Health monitor: {str(e)}")
        logger.error(f"Failed to initialize health monitor: {str(e)}")
    
    # Initialize performance logger
    try:
        from app.utils.performance_logger import get_performance_logger
        perf_logger = get_performance_logger()
        setup_status['performance_logger'] = True
        logger.info("Performance logger initialized")
    except Exception as e:
        setup_status['errors'].append(f"Performance logger: {str(e)}")
        logger.error(f"Failed to initialize performance logger: {str(e)}")
    
    # Initialize batch processor
    try:
        from app.utils.batch_processor import get_batch_processor
        batch_processor = get_batch_processor()
        setup_status['batch_processor'] = True
        logger.info("Batch processor initialized")
    except Exception as e:
        setup_status['errors'].append(f"Batch processor: {str(e)}")
        logger.error(f"Failed to initialize batch processor: {str(e)}")
    
    # Initialize database optimizer
    try:
        from app.utils.database_optimizer import get_database_optimizer
        db_optimizer = get_database_optimizer()
        setup_status['database_optimizer'] = True
        logger.info("Database optimizer initialized")
    except Exception as e:
        setup_status['errors'].append(f"Database optimizer: {str(e)}")
        logger.error(f"Failed to initialize database optimizer: {str(e)}")
    
    # Initialize ChromaDB performance monitor
    try:
        from app.utils.chroma_performance import get_chroma_monitor
        chroma_monitor = get_chroma_monitor()
        setup_status['chroma_performance'] = True
        logger.info("ChromaDB performance monitor initialized")
    except Exception as e:
        setup_status['errors'].append(f"ChromaDB performance: {str(e)}")
        logger.error(f"Failed to initialize ChromaDB performance monitor: {str(e)}")
    
    # Register performance routes with Flask app
    if app is not None:
        try:
            from app.routes.performance import performance_bp
            app.register_blueprint(performance_bp)
            setup_status['performance_routes'] = True
            logger.info("Performance routes registered")
        except Exception as e:
            setup_status['errors'].append(f"Performance routes: {str(e)}")
            logger.error(f"Failed to register performance routes: {str(e)}")
    
    # Setup request monitoring if Flask app is provided
    if app is not None and setup_status['performance_monitor']:
        setup_request_monitoring(app)
    
    # Log setup summary
    successful_components = sum(1 for k, v in setup_status.items() if k != 'errors' and v)
    total_components = len(setup_status) - 1  # Exclude 'errors' key
    
    logger.info(f"Performance monitoring setup complete: {successful_components}/{total_components} components initialized")
    
    if setup_status['errors']:
        logger.warning(f"Setup completed with {len(setup_status['errors'])} errors")
        for error in setup_status['errors']:
            logger.warning(f"  - {error}")
    
    return setup_status

def setup_request_monitoring(app):
    """Setup Flask request monitoring."""
    try:
        from flask import g, request
        import time
        
        @app.before_request
        def before_request():
            """Record request start time."""
            g.start_time = time.time()
        
        @app.after_request
        def after_request(response):
            """Log request performance."""
            if hasattr(g, 'start_time'):
                execution_time = time.time() - g.start_time
                
                # Log slow requests
                if execution_time > 2.0:  # Log requests taking more than 2 seconds
                    logger.warning(f"Slow request: {request.method} {request.path} took {execution_time:.3f}s")
                
                # Log all requests in debug mode
                if app.debug:
                    logger.debug(f"Request: {request.method} {request.path} - {execution_time:.3f}s")
            
            return response
        
        logger.info("Flask request monitoring enabled")
        
    except Exception as e:
        logger.error(f"Failed to setup request monitoring: {str(e)}")

def get_performance_status():
    """Get current performance monitoring status."""
    status = {
        'timestamp': time.time(),
        'components': {},
        'system_health': None,
        'recent_metrics_count': 0,
        'recent_alerts_count': 0
    }
    
    # Check performance monitor
    try:
        from app.utils.performance_monitor import get_performance_monitor
        perf_monitor = get_performance_monitor()
        if perf_monitor:
            recent_metrics = perf_monitor.get_recent_metrics(hours=1)
            status['recent_metrics_count'] = len(recent_metrics)
            status['components']['performance_monitor'] = 'active'
        else:
            status['components']['performance_monitor'] = 'inactive'
    except:
        status['components']['performance_monitor'] = 'error'
    
    # Check health monitor
    try:
        from app.utils.health_monitor import get_health_monitor
        health_monitor = get_health_monitor()
        if health_monitor:
            health_status = health_monitor.check_health_status()
            status['system_health'] = {
                'status': health_status.status,
                'score': health_status.score
            }
            status['components']['health_monitor'] = 'active'
        else:
            status['components']['health_monitor'] = 'inactive'
    except:
        status['components']['health_monitor'] = 'error'
    
    # Check performance logger
    try:
        from app.utils.performance_logger import get_performance_logger
        perf_logger = get_performance_logger()
        if perf_logger:
            recent_alerts = perf_logger.get_recent_alerts(hours=1)
            status['recent_alerts_count'] = len(recent_alerts)
            status['components']['performance_logger'] = 'active'
        else:
            status['components']['performance_logger'] = 'inactive'
    except:
        status['components']['performance_logger'] = 'error'
    
    # Check other components
    components = [
        ('batch_processor', 'app.utils.batch_processor', 'get_batch_processor'),
        ('database_optimizer', 'app.utils.database_optimizer', 'get_database_optimizer'),
        ('chroma_performance', 'app.utils.chroma_performance', 'get_chroma_monitor')
    ]
    
    for comp_name, module_name, func_name in components:
        try:
            module = __import__(module_name, fromlist=[func_name])
            func = getattr(module, func_name)
            instance = func()
            status['components'][comp_name] = 'active' if instance else 'inactive'
        except:
            status['components'][comp_name] = 'error'
    
    return status

def create_performance_summary():
    """Create a summary of the performance monitoring system."""
    import time
    
    summary = {
        'timestamp': time.time(),
        'system_info': {
            'python_version': sys.version,
            'platform': sys.platform,
            'working_directory': os.getcwd()
        },
        'performance_monitoring': get_performance_status(),
        'features': {
            'function_timing': True,
            'memory_tracking': True,
            'database_optimization': True,
            'batch_processing': True,
            'health_monitoring': True,
            'alerting': True,
            'web_dashboard': True,
            'structured_logging': True
        }
    }
    
    return summary

# Convenience function for easy integration
def enable_performance_monitoring(app=None):
    """
    Convenience function to enable performance monitoring.
    
    Usage:
        from app.performance_integration import enable_performance_monitoring
        enable_performance_monitoring(app)
    """
    return setup_performance_monitoring(app)
