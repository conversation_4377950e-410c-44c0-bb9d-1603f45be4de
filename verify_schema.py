#!/usr/bin/env python3
"""
Quick script to verify the enhanced PDF metadata database schema.
"""

import sys
import os
import sqlite3

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from models.schema import initialize_database

def verify_enhanced_metadata_schema():
    """Verify that the enhanced PDF metadata columns exist in the database."""
    
    print("🔍 Verifying Enhanced PDF Metadata Database Schema")
    print("=" * 60)
    
    # Initialize the database
    try:
        initialize_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    # Connect to the database (use the same path as schema.py)
    db_path = os.getenv("DB_PATH", "./erdb_main.db")
    print(f"📁 Database path: {db_path}")

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        print("✅ Connected to database")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    # Check what tables exist
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"\n📋 Database Tables: {tables}")

        if 'pdf_documents' not in tables:
            print("❌ pdf_documents table does not exist!")
            conn.close()
            return False

        # Check pdf_documents table schema
        cursor.execute('PRAGMA table_info(pdf_documents)')
        columns = [row[1] for row in cursor.fetchall()]

        print(f"\n📋 PDF Documents Table has {len(columns)} columns")
        
        # Check for enhanced metadata columns
        enhanced_columns = [
            'pdf_title', 'pdf_author', 'pdf_subject', 'pdf_keywords',
            'pdf_creation_date', 'pdf_modification_date', 'pdf_version',
            'pdf_producer', 'pdf_creator'
        ]
        
        print("\n🔍 Enhanced Metadata Columns:")
        all_present = True
        for col in enhanced_columns:
            if col in columns:
                print(f"  ✅ {col}")
            else:
                print(f"  ❌ {col} - MISSING")
                all_present = False
        
        # Check for indexes
        print("\n🔍 Database Indexes:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='pdf_documents'")
        indexes = [row[0] for row in cursor.fetchall()]
        
        expected_indexes = ['idx_pdf_title', 'idx_pdf_author', 'idx_pdf_subject']
        for idx in expected_indexes:
            if idx in indexes:
                print(f"  ✅ {idx}")
            else:
                print(f"  ❌ {idx} - MISSING")
        
        conn.close()
        
        if all_present:
            print("\n🎉 All enhanced metadata columns are present!")
            return True
        else:
            print("\n⚠️  Some enhanced metadata columns are missing!")
            return False
            
    except Exception as e:
        print(f"❌ Schema verification failed: {e}")
        conn.close()
        return False

if __name__ == "__main__":
    success = verify_enhanced_metadata_schema()
    sys.exit(0 if success else 1)
