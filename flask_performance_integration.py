
# Add this to your main Flask app file (e.g., app.py or __init__.py)

from app.routes.performance import performance_bp
from app.utils.performance_logger import get_performance_logger
from app.utils.health_monitor import get_health_monitor

# Register performance monitoring blueprint
app.register_blueprint(performance_bp)

# Initialize performance monitoring
performance_logger = get_performance_logger()
health_monitor = get_health_monitor()

# Optional: Add performance monitoring to all requests
@app.before_request
def before_request():
    from flask import g
    import time
    g.start_time = time.time()

@app.after_request
def after_request(response):
    from flask import g, request
    import time
    
    if hasattr(g, 'start_time'):
        execution_time = time.time() - g.start_time
        
        # Log slow requests
        if execution_time > 2.0:  # Log requests taking more than 2 seconds
            logger.warning(f"Slow request: {request.method} {request.path} took {execution_time:.3f}s")
    
    return response
