#!/usr/bin/env python3
"""
Test script for enhanced RAG PDF text extraction.
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_rag_extraction():
    """Test the enhanced RAG PDF text extraction."""
    
    # Test PDF path - use a sample PDF from the test files
    test_pdf_path = "test_files/CANOPY/canopy_v44n2.pdf"
    
    if not os.path.exists(test_pdf_path):
        logger.error(f"Test PDF not found: {test_pdf_path}")
        return False
    
    try:
        # Import the enhanced PDF processor
        from app.services.pdf_processor import (
            extract_text_with_rag,
            extract_text_with_advanced_columns,
            process_pdf,
            pdf_to_documents
        )
        
        logger.info("Testing enhanced RAG PDF text extraction...")
        
        # Test 1: RAG-enhanced text extraction
        logger.info("Test 1: RAG-enhanced text extraction")
        rag_results = extract_text_with_rag(
            test_pdf_path,
            category="CANOPY",
            save_text=True,
            use_context7=False,
            column_detection=True,
            sentence_orchestration=True,
            table_strategy='advanced',
            extract_words=True,
            debug=True
        )
        
        if rag_results:
            logger.info(f"✓ RAG extraction successful: {len(rag_results)} pages")
            total_chars = sum(len(page['text']) for page in rag_results)
            total_words = sum(page.get('metadata', {}).get('word_count', 0) for page in rag_results)
            logger.info(f"  Total characters: {total_chars}")
            logger.info(f"  Total words: {total_words}")
            
            # Show sample metadata
            if rag_results:
                sample_metadata = rag_results[0].get('metadata', {})
                logger.info(f"  Sample metadata: {sample_metadata}")
        else:
            logger.warning("✗ RAG extraction returned no results")
        
        # Test 2: Advanced column extraction
        logger.info("\nTest 2: Advanced column extraction")
        column_results = extract_text_with_advanced_columns(
            test_pdf_path,
            category="CANOPY",
            save_text=True,
            detect_columns=True,
            sort_text=True,
            debug=True
        )
        
        if column_results:
            logger.info(f"✓ Column extraction successful: {len(column_results)} pages")
            for page in column_results:
                metadata = page.get('metadata', {})
                column_count = metadata.get('column_count', 1)
                logger.info(f"  Page {page['page']}: {column_count} columns, {metadata.get('word_count', 0)} words")
        else:
            logger.warning("✗ Column extraction returned no results")
        
        # Test 3: Full PDF processing with RAG
        logger.info("\nTest 3: Full PDF processing with RAG")
        processed_pdf = process_pdf(
            test_pdf_path,
            category="CANOPY",
            extract_tables=False,
            save_images=False,
            save_tables=False
        )
        
        if processed_pdf and processed_pdf.get('text'):
            logger.info(f"✓ Full processing successful: {len(processed_pdf['text'])} pages")
            
            # Check extraction methods used
            extraction_methods = set()
            for page in processed_pdf['text']:
                method = page.get('extraction_method', 'unknown')
                extraction_methods.add(method)
            
            logger.info(f"  Extraction methods used: {extraction_methods}")
            
            # Test document conversion
            documents = pdf_to_documents(test_pdf_path, category="CANOPY")
            if documents:
                logger.info(f"✓ Document conversion successful: {len(documents)} documents")
                
                # Show sample document metadata
                if documents:
                    sample_doc = documents[0]
                    logger.info(f"  Sample document metadata: {sample_doc.metadata}")
            else:
                logger.warning("✗ Document conversion failed")
        else:
            logger.warning("✗ Full processing failed")
        
        logger.info("\n✓ All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test the RAG extraction configuration."""
    
    try:
        from config.rag_extraction_config import (
            get_rag_config, get_context7_config, get_column_detection_config,
            get_sentence_flow_config, is_rag_enabled, is_context7_enabled
        )
        
        logger.info("Testing RAG extraction configuration...")
        
        # Test configuration retrieval
        rag_config = get_rag_config()
        context7_config = get_context7_config()
        column_config = get_column_detection_config()
        sentence_config = get_sentence_flow_config()
        
        logger.info(f"✓ RAG config loaded: {len(rag_config)} settings")
        logger.info(f"✓ Context7 config loaded: {len(context7_config)} settings")
        logger.info(f"✓ Column config loaded: {len(column_config)} settings")
        logger.info(f"✓ Sentence config loaded: {len(sentence_config)} settings")
        
        # Test feature flags
        rag_enabled = is_rag_enabled()
        context7_enabled = is_context7_enabled()
        
        logger.info(f"  RAG extraction enabled: {rag_enabled}")
        logger.info(f"  Context7 integration enabled: {context7_enabled}")
        
        # Show key settings
        logger.info(f"  Table strategy: {rag_config.get('table_strategy')}")
        logger.info(f"  Column detection: {rag_config.get('column_detection')}")
        logger.info(f"  Sentence orchestration: {rag_config.get('sentence_orchestration')}")
        logger.info(f"  Extract words: {rag_config.get('extract_words')}")
        
        logger.info("✓ Configuration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    
    logger.info("Starting enhanced RAG PDF extraction tests...")
    
    # Test configuration first
    config_success = test_configuration()
    
    # Test extraction functionality
    extraction_success = test_rag_extraction()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    logger.info(f"Configuration test: {'✓ PASSED' if config_success else '✗ FAILED'}")
    logger.info(f"Extraction test: {'✓ PASSED' if extraction_success else '✗ FAILED'}")
    
    if config_success and extraction_success:
        logger.info("\n🎉 All tests passed! Enhanced RAG PDF extraction is working correctly.")
        return True
    else:
        logger.error("\n❌ Some tests failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 