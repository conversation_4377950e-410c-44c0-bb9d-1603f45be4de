# Document Naming Fixes Summary

## Issue Identified

The system was generating generic document references like "Document 5" and "Document 8" instead of using actual PDF filenames in citations. This was happening because:

1. **Inconsistent citation_filename processing** across different document processing paths
2. **LLM not following citation instructions** properly
3. **Missing explicit prohibition** of generic document numbering
4. **No post-processing validation** to catch and fix generic references

## Fixes Implemented

### 1. Standardized Citation Filename Processing

**File**: `app/services/embedding_service.py`
- **Issue**: `citation_filename` was being set to the full timestamped filename instead of the clean filename
- **Fix**: Added consistent timestamp removal logic across all processing paths
- **Code Changes**:
  ```python
  # Before: doc.metadata["citation_filename"] = filename
  # After: 
  citation_filename = os.path.basename(filename).split("_", 1)[1] if "_" in os.path.basename(filename) else filename
  doc.metadata["citation_filename"] = citation_filename
  ```

**Result**: All documents now have consistent citation filenames without timestamps (e.g., `canopy_vol45n1.pdf` instead of `20250706114854_canopy_vol45n1.pdf`)

### 2. Enhanced LLM Instructions

**File**: `app/services/query_service.py`
- **Issue**: LLM instructions didn't explicitly prohibit generic document numbering
- **Fix**: Added explicit prohibition and clear examples in both preamble and context
- **Code Changes**:
  ```python
  # Added to preamble:
  - CRITICAL: NEVER use generic document references like "Document 5", "Document 8", "Source 1", etc.
  - ALWAYS use the actual Citation Filename from the context for all document references
  
  # Added to context:
  context += "\n\nCRITICAL DOCUMENT REFERENCE RULES:"
  context += "\n- NEVER use generic document references like 'Document 5', 'Document 8', 'Source 1', etc."
  context += "\n- ALWAYS use the actual Citation Filename from the context"
  context += "\n- Examples of WRONG references: 'According to Document 5...', 'Document 8 states...', 'Source 3 shows...'"
  context += "\n- Examples of CORRECT references: 'According to [Citation Filename]...', 'The [Citation Filename] document states...'"
  ```

**Result**: LLM now receives explicit instructions to avoid generic document references

### 3. Post-Processing Generic Reference Fix

**File**: `app/services/query_service.py`
- **Issue**: No mechanism to catch and fix generic references that slip through
- **Fix**: Added `fix_generic_document_references()` function that automatically replaces generic references
- **Code Changes**:
  ```python
  def fix_generic_document_references(text: str, docs: List[Document]) -> str:
      # Build mapping from document index to citation filename
      doc_mapping = {}
      for i, doc in enumerate(docs):
          if doc.metadata.get("type") == "pdf":
              citation_filename = doc.metadata.get("citation_filename", doc.metadata.get("original_filename", ""))
              if citation_filename:
                  doc_mapping[i + 1] = citation_filename
      
      # Replace patterns like "Document 5", "Source 1", etc.
      patterns = [
          (r'Document\s+(\d+)', 'Document {}'),
          (r'Source\s+(\d+)', 'Source {}'),
          (r'Doc\s+(\d+)', 'Doc {}'),
      ]
      
      for pattern, template in patterns:
          def replace_generic_ref(match):
              doc_num = int(match.group(1))
              if doc_num in doc_mapping:
                  return doc_mapping[doc_num]
              return match.group(0)
          
          text = re.sub(pattern, replace_generic_ref, text, flags=re.IGNORECASE)
      
      return text
  ```

**Result**: Any generic references that slip through are automatically corrected to actual filenames

### 4. Integration with Query Processing

**File**: `app/services/query_service.py`
- **Issue**: Generic reference fix wasn't being applied to LLM responses
- **Fix**: Added call to `fix_generic_document_references()` in the post-processing pipeline
- **Code Changes**:
  ```python
  # Get answer
  answer = chain.invoke({"context": context, "question": question})
  
  # Post-processing: fix generic document references like "Document 5", "Document 8", etc.
  answer = fix_generic_document_references(answer, docs)
  ```

**Result**: All LLM responses are automatically cleaned of generic document references

## Testing and Verification

### Test Script Created
**File**: `test_document_naming_fix.py`
- Tests citation filename consistency across different processing paths
- Tests generic reference fix function with sample data
- Verifies LLM instruction availability

### Test Results
```
✅ Citation Filename Consistency PASSED
✅ Generic Reference Fix PASSED  
✅ LLM Instructions PASSED
```

**Sample Test Output**:
```
Original text: According to Document 1, watershed vulnerability varies with physical properties.
Fixed text: According to canopy_vol45n1.pdf, watershed vulnerability varies with physical properties.
```

## Expected Results

### Before Fixes
- LLM generated: "According to Document 5, a watershed's vulnerability varies..."
- LLM generated: "Document 8 provides a summary procedure for vulnerability assessment..."
- Inconsistent citation filenames across different processing paths
- No automatic correction of generic references

### After Fixes
- LLM generates: "According to canopy_vol45n1.pdf, a watershed's vulnerability varies..."
- LLM generates: "manual_v31n1.pdf provides a summary procedure for vulnerability assessment..."
- Consistent citation filenames across all processing paths
- Automatic correction of any generic references that slip through

## Files Modified

1. **`app/services/embedding_service.py`** - Fixed citation filename processing
2. **`app/services/query_service.py`** - Enhanced LLM instructions and added post-processing fix
3. **`test_document_naming_fix.py`** - New test script for verification
4. **`DOCUMENT_NAMING_FIXES_SUMMARY.md`** - This documentation

## Configuration Notes

- Citation filenames are automatically cleaned of timestamps
- Generic document references are automatically detected and corrected
- LLM receives explicit instructions to avoid generic numbering
- All fixes are backward compatible and don't affect existing functionality

## Next Steps

1. **Monitor LLM responses** to verify generic references are eliminated
2. **Test with real queries** to ensure citation links work correctly
3. **Update user documentation** if needed to reflect improved citation quality
4. **Consider adding more test cases** for edge cases and different document types

## Verification Commands

To verify the fixes work:
```bash
# Run the test suite
python test_document_naming_fix.py

# Check that citation filenames are consistent
# Submit test queries and verify no "Document X" references appear
``` 