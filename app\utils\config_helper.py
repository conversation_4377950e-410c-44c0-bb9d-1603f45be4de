"""
Configuration Helper for ERDB Performance Monitoring

This module provides configuration utilities that work with different ports
and deployment environments, removing hardcoded references.
"""

import os
from typing import Optional, Dict, Any

def get_app_config() -> Dict[str, Any]:
    """Get application configuration including port and host settings."""
    return {
        'host': os.getenv('FLASK_HOST', '0.0.0.0'),
        'port': int(os.getenv('FLASK_PORT', '8080')),  # Default to 8080 instead of 5000
        'debug': os.getenv('FLASK_ENV', 'production') == 'development',
        'base_url': get_base_url()
    }

def get_base_url() -> str:
    """Get the base URL for the application."""
    host = os.getenv('FLASK_HOST', 'localhost')
    port = int(os.getenv('FLASK_PORT', '8080'))
    
    # Handle different deployment scenarios
    if host == '0.0.0.0':
        host = 'localhost'
    
    # Check if we're behind a proxy or using standard ports
    if port in [80, 443]:
        return f"http{'s' if port == 443 else ''}://{host}"
    else:
        return f"http://{host}:{port}"

def get_performance_dashboard_url() -> str:
    """Get the URL for the performance dashboard."""
    base_url = get_base_url()
    return f"{base_url}/admin/health"

def get_api_base_url() -> str:
    """Get the base URL for API endpoints."""
    base_url = get_base_url()
    return f"{base_url}/admin/health/api"

def update_environment_config():
    """Update environment configuration for performance monitoring."""
    config_updates = {
        # Use port 8080 as default instead of 5000
        'FLASK_PORT': '8080',
        'FLASK_HOST': '0.0.0.0',
        
        # Performance monitoring settings
        'PERFORMANCE_MONITORING_ENABLED': 'true',
        'PERFORMANCE_DASHBOARD_INTEGRATED': 'true',
        
        # Database optimization settings
        'DATABASE_OPTIMIZATION_ENABLED': 'true',
        'AUTO_INDEX_CREATION': 'true',
        
        # Batch processing settings
        'BATCH_PROCESSING_ENABLED': 'true',
        'BATCH_MAX_WORKERS': str(os.cpu_count() or 4),
        
        # Health monitoring settings
        'HEALTH_MONITORING_ENABLED': 'true',
        'HEALTH_CHECK_INTERVAL': '300',  # 5 minutes
        
        # Alert settings
        'PERFORMANCE_ALERTS_ENABLED': 'true',
        'ALERT_COOLDOWN_MINUTES': '15'
    }
    
    env_file = '.env'
    existing_vars = {}
    
    # Read existing .env file if it exists
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    existing_vars[key] = value
    
    # Add new variables that don't exist
    new_vars = {}
    for key, value in config_updates.items():
        if key not in existing_vars:
            new_vars[key] = value
    
    if new_vars:
        with open(env_file, 'a') as f:
            f.write(f"\n# Performance monitoring configuration updated\n")
            for key, value in new_vars.items():
                f.write(f"{key}={value}\n")
        
        return new_vars
    
    return {}

def is_performance_monitoring_enabled() -> bool:
    """Check if performance monitoring is enabled."""
    return os.getenv('PERFORMANCE_MONITORING_ENABLED', 'true').lower() == 'true'

def is_dashboard_integrated() -> bool:
    """Check if performance dashboard is integrated with admin interface."""
    return os.getenv('PERFORMANCE_DASHBOARD_INTEGRATED', 'true').lower() == 'true'

def get_performance_config() -> Dict[str, Any]:
    """Get performance monitoring configuration."""
    return {
        'enabled': is_performance_monitoring_enabled(),
        'dashboard_integrated': is_dashboard_integrated(),
        'dashboard_url': get_performance_dashboard_url(),
        'api_base_url': get_api_base_url(),
        'auto_refresh_interval': int(os.getenv('PERFORMANCE_AUTO_REFRESH_SECONDS', '30')),
        'metrics_retention_hours': int(os.getenv('PERFORMANCE_METRICS_RETENTION_HOURS', '24')),
        'alert_cooldown_minutes': int(os.getenv('ALERT_COOLDOWN_MINUTES', '15'))
    }

def get_database_config() -> Dict[str, Any]:
    """Get database optimization configuration."""
    return {
        'optimization_enabled': os.getenv('DATABASE_OPTIMIZATION_ENABLED', 'true').lower() == 'true',
        'auto_index_creation': os.getenv('AUTO_INDEX_CREATION', 'true').lower() == 'true',
        'vacuum_interval_hours': int(os.getenv('DATABASE_VACUUM_INTERVAL_HOURS', '24')),
        'analyze_interval_hours': int(os.getenv('DATABASE_ANALYZE_INTERVAL_HOURS', '6'))
    }

def get_batch_processing_config() -> Dict[str, Any]:
    """Get batch processing configuration."""
    return {
        'enabled': os.getenv('BATCH_PROCESSING_ENABLED', 'true').lower() == 'true',
        'max_workers': int(os.getenv('BATCH_MAX_WORKERS', str(os.cpu_count() or 4))),
        'default_chunk_size': int(os.getenv('BATCH_DEFAULT_CHUNK_SIZE', '10')),
        'cpu_threshold': float(os.getenv('BATCH_CPU_THRESHOLD', '80.0')),
        'memory_threshold': float(os.getenv('BATCH_MEMORY_THRESHOLD', '85.0')),
        'cache_enabled': os.getenv('BATCH_CACHE_ENABLED', 'true').lower() == 'true'
    }

def get_health_monitoring_config() -> Dict[str, Any]:
    """Get health monitoring configuration."""
    return {
        'enabled': os.getenv('HEALTH_MONITORING_ENABLED', 'true').lower() == 'true',
        'check_interval_seconds': int(os.getenv('HEALTH_CHECK_INTERVAL', '300')),
        'cpu_warning_threshold': float(os.getenv('HEALTH_CPU_WARNING_THRESHOLD', '80.0')),
        'cpu_critical_threshold': float(os.getenv('HEALTH_CPU_CRITICAL_THRESHOLD', '95.0')),
        'memory_warning_threshold': float(os.getenv('HEALTH_MEMORY_WARNING_THRESHOLD', '85.0')),
        'memory_critical_threshold': float(os.getenv('HEALTH_MEMORY_CRITICAL_THRESHOLD', '95.0')),
        'disk_warning_threshold': float(os.getenv('HEALTH_DISK_WARNING_THRESHOLD', '85.0')),
        'disk_critical_threshold': float(os.getenv('HEALTH_DISK_CRITICAL_THRESHOLD', '95.0'))
    }

def print_configuration_summary():
    """Print a summary of the current configuration."""
    app_config = get_app_config()
    perf_config = get_performance_config()
    
    print("="*60)
    print("ERDB PERFORMANCE MONITORING CONFIGURATION")
    print("="*60)
    print(f"Application URL: {app_config['base_url']}")
    print(f"Performance Dashboard: {perf_config['dashboard_url']}")
    print(f"API Base URL: {perf_config['api_base_url']}")
    print(f"Performance Monitoring: {'Enabled' if perf_config['enabled'] else 'Disabled'}")
    print(f"Dashboard Integration: {'Enabled' if perf_config['dashboard_integrated'] else 'Disabled'}")
    print("="*60)

def validate_configuration() -> Dict[str, Any]:
    """Validate the current configuration and return any issues."""
    issues = []
    warnings = []
    
    # Check port configuration
    port = int(os.getenv('FLASK_PORT', '8080'))
    if port == 5000:
        warnings.append("Using default Flask port 5000. Consider using port 8080 for production.")
    
    # Check performance monitoring dependencies
    try:
        import psutil
    except ImportError:
        issues.append("psutil package not installed - required for system monitoring")
    
    # Check if performance monitoring is properly configured
    if not is_performance_monitoring_enabled():
        warnings.append("Performance monitoring is disabled")
    
    # Check database configuration
    db_config = get_database_config()
    if not db_config['optimization_enabled']:
        warnings.append("Database optimization is disabled")
    
    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'warnings': warnings,
        'configuration': {
            'app': get_app_config(),
            'performance': get_performance_config(),
            'database': get_database_config(),
            'batch_processing': get_batch_processing_config(),
            'health_monitoring': get_health_monitoring_config()
        }
    }
